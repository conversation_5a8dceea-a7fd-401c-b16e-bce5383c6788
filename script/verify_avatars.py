#!/usr/bin/env python3
"""
Avatar Verification Script

This script verifies that avatars have been uploaded correctly for user profiles.
It checks the first few records to ensure the avatar upload process worked.

Usage:
    python3 verify_avatars.py [--limit N] [--csv path]
"""

import argparse
import csv
import json
import os
import requests
import sys

# Configuration
BASE_URL = "https://cms.copula.site"
SESSION_TOKEN = "pcTMayE-BZ7kqVkF9Va6wlhIe5D5KVGO"
DEFAULT_CSV_PATH = "new_data.csv"

# Headers for API requests
HEADERS = {
    'accept': 'application/json, text/plain, */*',
    'Cookie': f'directus_session_token={SESSION_TOKEN}'
}

def get_user_profile_by_sample_id(sample_id):
    """
    Retrieve user profile by sample_id using the filter API
    """
    url = f"{BASE_URL}/items/UserProfile"
    params = {
        'filter[sample_id][_eq]': sample_id
    }
    
    try:
        response = requests.get(url, headers=HEADERS, params=params)
        response.raise_for_status()
        
        data = response.json()
        if data.get('data') and len(data['data']) > 0:
            return data['data'][0]
        else:
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error retrieving profile for sample_id {sample_id}: {e}")
        return None

def verify_user_avatar(csv_row):
    """
    Verify avatar for a single user
    Returns: (has_avatar, avatar_id, profile_info)
    """
    sample_id = csv_row['id']
    file_name = csv_row['file_name']
    first_name = csv_row['first_name']
    last_name = csv_row['last_name']
    
    # Get user profile
    profile = get_user_profile_by_sample_id(sample_id)
    if not profile:
        return False, None, f"Profile not found for {first_name} {last_name} (ID: {sample_id})"
    
    profile_id = profile['id']
    avatar_id = profile.get('avatar')
    
    profile_info = f"{first_name} {last_name} (ID: {sample_id}, Profile: {profile_id})"
    
    if avatar_id:
        return True, avatar_id, profile_info
    else:
        return False, None, profile_info

def main():
    parser = argparse.ArgumentParser(description='Verify avatar uploads for user profiles')
    parser.add_argument('--limit', type=int, default=10, help='Number of records to verify (default: 10)')
    parser.add_argument('--csv', default=DEFAULT_CSV_PATH, help='Path to CSV file')
    
    args = parser.parse_args()
    
    csv_path = args.csv
    if not os.path.exists(csv_path):
        print(f"❌ CSV file not found: {csv_path}")
        sys.exit(1)
    
    print(f"🔍 Verifying avatar uploads...")
    print(f"📊 CSV file: {csv_path}")
    print(f"🌐 API Base URL: {BASE_URL}")
    print(f"📋 Checking first {args.limit} records")
    
    # Read CSV file
    records = []
    with open(csv_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        records = list(reader)
    
    total_records = len(records)
    records_to_check = records[:args.limit]
    
    print(f"\n{'='*80}")
    print("🔍 AVATAR VERIFICATION RESULTS")
    print(f"{'='*80}")
    
    has_avatar_count = 0
    no_avatar_count = 0
    error_count = 0
    
    for i, record in enumerate(records_to_check):
        print(f"\n📝 Record {i+1}:")
        
        try:
            has_avatar, avatar_id, profile_info = verify_user_avatar(record)
            
            if has_avatar:
                print(f"✅ {profile_info}")
                print(f"   🖼️  Avatar ID: {avatar_id}")
                print(f"   📁 Expected file: {record['file_name']}")
                has_avatar_count += 1
            else:
                print(f"❌ {profile_info}")
                print(f"   📁 Expected file: {record['file_name']}")
                print(f"   ⚠️  No avatar found!")
                no_avatar_count += 1
                
        except Exception as e:
            print(f"💥 Error checking record {i+1}: {e}")
            error_count += 1
    
    # Summary
    print(f"\n{'='*80}")
    print("📊 VERIFICATION SUMMARY")
    print(f"{'='*80}")
    print(f"✅ Profiles with avatars: {has_avatar_count}")
    print(f"❌ Profiles without avatars: {no_avatar_count}")
    print(f"💥 Errors: {error_count}")
    print(f"📋 Total checked: {len(records_to_check)}")
    
    if len(records_to_check) > 0:
        success_rate = (has_avatar_count / len(records_to_check)) * 100
        print(f"📈 Avatar coverage: {success_rate:.1f}%")
    
    if no_avatar_count > 0:
        print(f"\n⚠️  {no_avatar_count} profiles are missing avatars!")
        print("💡 Run the avatar_uploader.py script to upload missing avatars.")
    else:
        print(f"\n🎉 All checked profiles have avatars!")

if __name__ == "__main__":
    main()
