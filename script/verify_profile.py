#!/usr/bin/env python3
"""
Script to verify that a user profile was created and updated correctly.
"""

import requests
import json

# Configuration
BASE_URL = "https://cms.copula.site"
SESSION_TOKEN = "pcTMayE-BZ7kqVkF9Va6wlhIe5D5KVGO"

# Headers for API requests
HEADERS = {
    'accept': 'application/json, text/plain, */*',
    'Cookie': f'directus_session_token={SESSION_TOKEN}'
}

def get_user_profile_by_sample_id(sample_id):
    """
    Get user profile by sample_id
    
    Args:
        sample_id (int): Sample ID to search for
        
    Returns:
        dict: Profile data if found, None if not found
    """
    url = f"{BASE_URL}/items/UserProfile"
    params = {
        'filter[sample_id][_eq]': sample_id
    }
    
    try:
        response = requests.get(url, headers=HEADERS, params=params)
        response.raise_for_status()
        
        result = response.json()
        data = result.get('data', [])
        
        if data:
            return data[0]
        else:
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"✗ Failed to get profile for sample_id {sample_id}: {e}")
        return None

def main():
    """Main function to verify the test profile"""
    print("🔍 Profile Verification Script")
    print()

    # Check both profiles we created
    sample_ids = [11462, 12327]

    for sample_id in sample_ids:
        print(f"\nChecking profile with sample_id: {sample_id}")
        print("-" * 50)

        profile = get_user_profile_by_sample_id(sample_id)

        if profile:
            print("✅ Profile found!")

            # Check key fields
            print(f"🔍 Key field verification:")
            print(f"  - sample_id: {profile.get('sample_id')}")
            print(f"  - first_name: {profile.get('first_name')}")
            print(f"  - last_name: {profile.get('last_name')}")
            print(f"  - age: {profile.get('age')}")
            print(f"  - height: {profile.get('height')}")
            print(f"  - race: {profile.get('race')}")
            print(f"  - gender: {profile.get('gender')}")
            print(f"  - education: {profile.get('education')}")
            print(f"  - desired_relationship: {profile.get('desired_relationship')}")
            print(f"  - essay length: {len(profile.get('essay', '')) if profile.get('essay') else 0} characters")

        else:
            print("❌ Profile not found!")

if __name__ == "__main__":
    main()
