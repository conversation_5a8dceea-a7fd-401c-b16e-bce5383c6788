#!/usr/bin/env python3
"""
Script to upload all images from ./files/profile_pic_processed/ directly to 
Google Cloud Storage bucket comfyui-data-analytic-project-424703/comfyui/common/
preserving original filenames.
"""

import os
import glob
import time
import json
from pathlib import Path
from google.cloud import storage
from google.oauth2 import service_account
from google.auth.exceptions import DefaultCredentialsError

# Configuration
BUCKET_NAME = "comfyui-data-analytic-project-424703"
GCS_FOLDER = "comfyui/common/"
SOURCE_FOLDER = "./files/profile_pic_processed/"
CREDENTIALS_PATH = "gcp-credentials.json"
BATCH_SIZE = 10  # Number of files to upload in each batch
DELAY_BETWEEN_BATCHES = 1  # Seconds to wait between batches

def get_all_image_files(folder_path):
    """Get all image files from the specified folder."""
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.webp', '*.bmp', '*.tiff', '*.tif']
    all_files = []
    
    for extension in image_extensions:
        pattern = os.path.join(folder_path, extension)
        files = glob.glob(pattern, recursive=False)
        all_files.extend(files)
    
    # Remove any non-image files (like processed_log.txt)
    image_files = [f for f in all_files if not f.endswith('.txt')]
    
    return sorted(image_files)

def initialize_gcs_client():
    """Initialize Google Cloud Storage client using the same method as main.py."""
    try:
        print(f"🔐 Initializing GCS client with credentials: {CREDENTIALS_PATH}")

        # Use explicit credentials file (same as main.py)
        if os.path.exists(CREDENTIALS_PATH):
            print(f"✅ Found credentials file: {CREDENTIALS_PATH}")
            credentials = service_account.Credentials.from_service_account_file(CREDENTIALS_PATH)
            client = storage.Client(credentials=credentials)
            print(f"✅ Successfully initialized GCP Storage client with credentials from {CREDENTIALS_PATH}")
        else:
            print(f"❌ Credentials file not found: {CREDENTIALS_PATH}")
            return None, None

        # Test the connection by trying to get the bucket
        bucket = client.bucket(BUCKET_NAME)

        # Check if bucket exists
        try:
            bucket_exists = bucket.exists()
            if bucket_exists:
                print(f"✅ Successfully connected to GCS bucket: {BUCKET_NAME}")
            else:
                print(f"❌ Bucket {BUCKET_NAME} does not exist")
                return None, None
        except Exception as e:
            # Handle permission error gracefully (same as gcp_storage.py)
            print(f"⚠️  Unable to check if bucket {BUCKET_NAME} exists due to permissions: {str(e)}")
            print(f"✅ Assuming bucket {BUCKET_NAME} exists and continuing")

        return client, bucket

    except Exception as e:
        print(f"❌ Error connecting to GCS: {str(e)}")
        return None, None

def upload_single_file(bucket, local_file_path, gcs_file_path):
    """Upload a single file to Google Cloud Storage."""
    try:
        file_name = os.path.basename(local_file_path)
        print(f"Uploading: {file_name} -> {gcs_file_path}")
        
        # Create a blob object
        blob = bucket.blob(gcs_file_path)
        
        # Upload the file
        start_time = time.time()
        blob.upload_from_filename(local_file_path)
        upload_time = time.time() - start_time
        
        # Get file size for reporting
        file_size = os.path.getsize(local_file_path)
        file_size_mb = file_size / (1024 * 1024)
        
        print(f"✅ Successfully uploaded: {file_name}")
        print(f"   Size: {file_size_mb:.2f} MB, Time: {upload_time:.2f}s")
        print(f"   GCS Path: gs://{BUCKET_NAME}/{gcs_file_path}")
        
        return {
            'success': True,
            'local_file': local_file_path,
            'gcs_path': gcs_file_path,
            'file_name': file_name,
            'file_size_mb': file_size_mb,
            'upload_time_seconds': upload_time,
            'public_url': f"gs://{BUCKET_NAME}/{gcs_file_path}"
        }
        
    except Exception as e:
        print(f"❌ Failed to upload {file_name}: {str(e)}")
        return {
            'success': False,
            'local_file': local_file_path,
            'gcs_path': gcs_file_path,
            'file_name': file_name,
            'error': str(e)
        }

def upload_files_in_batches(bucket, file_list):
    """Upload files in batches to avoid overwhelming the connection."""
    total_files = len(file_list)
    successful_uploads = []
    failed_uploads = []
    total_size_mb = 0
    
    print(f"Starting upload of {total_files} files in batches of {BATCH_SIZE}")
    print("=" * 70)
    
    for i in range(0, total_files, BATCH_SIZE):
        batch = file_list[i:i + BATCH_SIZE]
        batch_num = (i // BATCH_SIZE) + 1
        total_batches = (total_files + BATCH_SIZE - 1) // BATCH_SIZE
        
        print(f"\nBatch {batch_num}/{total_batches} - Processing {len(batch)} files:")
        print("-" * 50)
        
        batch_start_time = time.time()
        
        for local_file_path in batch:
            # Get original filename
            original_filename = os.path.basename(local_file_path)
            
            # Create GCS path preserving original filename
            gcs_file_path = f"{GCS_FOLDER}{original_filename}"
            
            # Upload the file
            result = upload_single_file(bucket, local_file_path, gcs_file_path)
            
            if result['success']:
                successful_uploads.append(result)
                total_size_mb += result['file_size_mb']
            else:
                failed_uploads.append(result)
            
            # Small delay between individual uploads
            time.sleep(0.2)
        
        batch_time = time.time() - batch_start_time
        print(f"\nBatch {batch_num} completed in {batch_time:.2f} seconds")
        
        # Delay between batches (except for the last batch)
        if i + BATCH_SIZE < total_files:
            print(f"Waiting {DELAY_BETWEEN_BATCHES} seconds before next batch...")
            time.sleep(DELAY_BETWEEN_BATCHES)
    
    return successful_uploads, failed_uploads, total_size_mb

def save_results_to_file(successful_uploads, failed_uploads):
    """Save upload results to JSON files."""
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    
    # Save successful uploads
    if successful_uploads:
        success_file = f"gcs_successful_uploads_{timestamp}.json"
        with open(success_file, 'w') as f:
            json.dump(successful_uploads, f, indent=2)
        print(f"\n✅ Successful uploads saved to: {success_file}")
    
    # Save failed uploads
    if failed_uploads:
        failed_file = f"gcs_failed_uploads_{timestamp}.json"
        with open(failed_file, 'w') as f:
            json.dump(failed_uploads, f, indent=2)
        print(f"❌ Failed uploads saved to: {failed_file}")

def check_existing_files(bucket, file_list):
    """Check which files already exist in GCS to avoid re-uploading."""
    print("🔍 Checking for existing files in GCS...")
    
    existing_files = set()
    files_to_upload = []
    
    # List all blobs in the target folder
    try:
        blobs = bucket.list_blobs(prefix=GCS_FOLDER)
        for blob in blobs:
            # Extract filename from the blob name
            filename = blob.name.replace(GCS_FOLDER, "")
            if filename:  # Skip empty names (folder itself)
                existing_files.add(filename)
        
        print(f"Found {len(existing_files)} existing files in gs://{BUCKET_NAME}/{GCS_FOLDER}")
        
        # Filter out files that already exist
        for local_file_path in file_list:
            filename = os.path.basename(local_file_path)
            if filename in existing_files:
                print(f"⏭️  Skipping existing file: {filename}")
            else:
                files_to_upload.append(local_file_path)
        
        print(f"📋 {len(files_to_upload)} new files to upload")
        return files_to_upload
        
    except Exception as e:
        print(f"⚠️  Could not check existing files: {str(e)}")
        print("Proceeding with all files (may overwrite existing files)")
        return file_list

def main():
    """Main function to orchestrate the upload process."""
    print("🚀 Starting direct upload to Google Cloud Storage")
    print(f"Source folder: {SOURCE_FOLDER}")
    print(f"Target: gs://{BUCKET_NAME}/{GCS_FOLDER}")
    print("=" * 70)
    
    # Check if source folder exists
    if not os.path.exists(SOURCE_FOLDER):
        print(f"❌ Source folder does not exist: {SOURCE_FOLDER}")
        return
    
    # Get all image files
    image_files = get_all_image_files(SOURCE_FOLDER)
    
    if not image_files:
        print(f"❌ No image files found in {SOURCE_FOLDER}")
        return
    
    print(f"📁 Found {len(image_files)} image files to upload")
    
    # Initialize GCS client
    client, bucket = initialize_gcs_client()
    if not client or not bucket:
        return
    
    # Check for existing files to avoid re-uploading
    files_to_upload = check_existing_files(bucket, image_files)
    
    if not files_to_upload:
        print("🎉 All files already exist in GCS. Nothing to upload!")
        return
    
    # Start the upload process
    print(f"\n🔄 Starting upload of {len(files_to_upload)} files...")
    start_time = time.time()
    successful_uploads, failed_uploads, total_size_mb = upload_files_in_batches(bucket, files_to_upload)
    end_time = time.time()
    
    # Print summary
    print("\n" + "=" * 70)
    print("📊 UPLOAD SUMMARY")
    print("=" * 70)
    print(f"Total files to upload: {len(files_to_upload)}")
    print(f"Successful uploads: {len(successful_uploads)}")
    print(f"Failed uploads: {len(failed_uploads)}")
    print(f"Success rate: {(len(successful_uploads) / len(files_to_upload)) * 100:.1f}%")
    print(f"Total data uploaded: {total_size_mb:.2f} MB")
    print(f"Total time: {end_time - start_time:.1f} seconds")
    print(f"Average speed: {total_size_mb / (end_time - start_time):.2f} MB/s")
    
    # Save results to files
    save_results_to_file(successful_uploads, failed_uploads)
    
    if failed_uploads:
        print(f"\n⚠️  {len(failed_uploads)} files failed to upload. Check the gcs_failed_uploads_*.json file for details.")
    else:
        print(f"\n🎉 All files uploaded successfully to gs://{BUCKET_NAME}/{GCS_FOLDER}")

if __name__ == "__main__":
    main()
