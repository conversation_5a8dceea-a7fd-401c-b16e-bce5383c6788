### 1. here is the curl to create new user:

curl --location 'https://cms.copula.site/users' \
--header 'accept: application/json, text/plain, */*' \
--header 'content-type: application/json' \
--header 'Cookie: directus_session_token=pcTMayE-BZ7kqVkF9Va6wlhIe5D5KVGO' \
--data-raw '{"role":"b9a6dffa-062b-4a24-b838-8b93d4a91147","email":"<EMAIL>","password":"123456"}'

when create new user, only change its email (like sample3, sample4, etc), everything else can be the same.

sample response:
{
    "data": {
        "id": "5856ef4e-f850-42fe-8ea7-1aaecaff4258",
        "email": "<EMAIL>",
        "password": "**********",
        "status": "active"
    }
}

we can extract its user_id from data.id to use for next step.
### 2. the curl to read user profile:

curl --location  'https://cms.copula.site/items/UserProfile?filter\[user\]\[_eq\]={USER_ID}' \
--header 'accept: application/json, text/plain, */*' \
--header 'Cookie: directus_session_token=pcTMayE-BZ7kqVkF9Va6wlhIe5D5KVGO'

which USER_ID is the user_id we got from API 1

sample response:

{
    "data": [
        {
            "id": 4,
            "status": "published",
            "user": "5856ef4e-f850-42fe-8ea7-1aaecaff4258",
            "first_name": null,
            "last_name": null,
            "height": null,
            "marriage_status": null,
            "race": null,
            "age": null,
            "avatar": null,
            "desired_relationship": null,
            "essay": null,
            "smoke": null,
            "pets": null,
            "income": null,
            "education": null,
            "drink": null,
            "body_type": null,
            "gender": null,
            "sample_id": null,
            "city": null,
            "work": null,
            "language": null,
            "religion": null,
            "openness": null,
            "conscientiousness": null,
            "extraversion": null,
            "agreeableness": null,
            "neuroticism": null,
            "traditionalism": null,
            "conformity": null,
            "interests": null,
            "family_plan": null,
            "images": []
        }
    ]
}

From here, we can see the user_profile_id is 4, we will use this id for next step. We also know all the fields that we can update.

### 3. the curl to update user profile:

curl --location --request PATCH 'https://cms.copula.site/items/UserProfile/{USER_PROFILE_ID}' \
--header 'accept: application/json, text/plain, */*' \
--header 'content-type: application/json' \
--header 'Cookie: directus_session_token=pcTMayE-BZ7kqVkF9Va6wlhIe5D5KVGO' \
--data '{"age":34,"first_name":"My first name","race":"asian"}'

all other fields can be included in the same way.

### now write a python script in this folder, read the file 