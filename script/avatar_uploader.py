#!/usr/bin/env python3
"""
Avatar Uploader Script

This script uploads avatar images for user profiles based on the strategy outlined in strategy.md.
It performs the following steps for each user:
1. Check if user profile already has an avatar
2. If not, upload the corresponding image file
3. Update the user profile with the uploaded image ID

Usage:
    python3 avatar_uploader.py [--start N] [--limit N] [--csv path] [--test]
"""

import argparse
import csv
import json
import os
import requests
import sys
import time
from pathlib import Path

# Configuration
BASE_URL = "https://cms.copula.site"
SESSION_TOKEN = "pcTMayE-BZ7kqVkF9Va6wlhIe5D5KVGO"
DEFAULT_CSV_PATH = "new_data.csv"
IMAGE_FOLDER = "/Users/<USER>/Documents/hailstone/copula_backend/files/face_photos_2k"

# Headers for API requests
HEADERS = {
    'accept': 'application/json, text/plain, */*',
    'Cookie': f'directus_session_token={SESSION_TOKEN}'
}

HEADERS_JSON = {
    'accept': 'application/json, text/plain, */*',
    'content-type': 'application/json',
    'Cookie': f'directus_session_token={SESSION_TOKEN}'
}

def get_user_profile_by_sample_id(sample_id):
    """
    Retrieve user profile by sample_id using the filter API
    """
    url = f"{BASE_URL}/items/UserProfile"
    params = {
        'filter[sample_id][_eq]': sample_id
    }
    
    try:
        response = requests.get(url, headers=HEADERS, params=params)
        response.raise_for_status()
        
        data = response.json()
        if data.get('data') and len(data['data']) > 0:
            return data['data'][0]
        else:
            print(f"❌ No profile found for sample_id: {sample_id}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error retrieving profile for sample_id {sample_id}: {e}")
        return None

def upload_image_file(file_path):
    """
    Upload an image file to the CMS and return the image ID
    """
    url = f"{BASE_URL}/files"
    
    if not os.path.exists(file_path):
        print(f"❌ Image file not found: {file_path}")
        return None
    
    try:
        with open(file_path, 'rb') as f:
            files = {'file': f}
            response = requests.post(url, headers={'Cookie': f'directus_session_token={SESSION_TOKEN}'}, files=files)
            response.raise_for_status()
            
            data = response.json()
            if data.get('data') and data['data'].get('id'):
                image_id = data['data']['id']
                print(f"✅ Image uploaded successfully: {image_id}")
                return image_id
            else:
                print(f"❌ Failed to upload image: {file_path}")
                return None
                
    except requests.exceptions.RequestException as e:
        print(f"❌ Error uploading image {file_path}: {e}")
        return None

def update_profile_avatar(profile_id, image_id):
    """
    Update user profile with avatar image ID
    """
    url = f"{BASE_URL}/items/UserProfile/{profile_id}"
    payload = {"avatar": image_id}
    
    try:
        response = requests.patch(url, headers=HEADERS_JSON, json=payload)
        response.raise_for_status()
        
        print(f"✅ Profile {profile_id} updated with avatar {image_id}")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Error updating profile {profile_id} with avatar: {e}")
        return False

def process_user_avatar(csv_row):
    """
    Process avatar upload for a single user
    Returns: (success, skip_reason)
    """
    sample_id = csv_row['id']
    file_name = csv_row['file_name']
    first_name = csv_row['first_name']
    last_name = csv_row['last_name']
    
    print(f"\n🔄 Processing {first_name} {last_name} (ID: {sample_id})")
    
    # Step 1: Get user profile
    profile = get_user_profile_by_sample_id(sample_id)
    if not profile:
        return False, "Profile not found"
    
    profile_id = profile['id']
    current_avatar = profile.get('avatar')
    
    # Step 2: Check if avatar already exists
    if current_avatar:
        print(f"⏭️  Profile {profile_id} already has avatar: {current_avatar}")
        return True, "Avatar already exists"
    
    # Step 3: Check if image file exists
    image_path = os.path.join(IMAGE_FOLDER, file_name)
    if not os.path.exists(image_path):
        print(f"❌ Image file not found: {image_path}")
        return False, "Image file not found"
    
    # Step 4: Upload image
    print(f"📤 Uploading image: {file_name}")
    image_id = upload_image_file(image_path)
    if not image_id:
        return False, "Image upload failed"
    
    # Step 5: Update profile with avatar
    print(f"🔄 Updating profile {profile_id} with avatar {image_id}")
    success = update_profile_avatar(profile_id, image_id)
    
    if success:
        print(f"✅ Successfully processed {first_name} {last_name}")
        return True, "Success"
    else:
        return False, "Profile update failed"

def main():
    parser = argparse.ArgumentParser(description='Upload avatar images for user profiles')
    parser.add_argument('--start', type=int, default=0, help='Start from record number (0-based)')
    parser.add_argument('--limit', type=int, help='Limit number of records to process')
    parser.add_argument('--csv', default=DEFAULT_CSV_PATH, help='Path to CSV file')
    parser.add_argument('--test', action='store_true', help='Test mode - process only first record')
    
    args = parser.parse_args()
    
    # Test mode overrides
    if args.test:
        args.start = 0
        args.limit = 1
        print("🧪 Running in TEST MODE - processing only first record")
    
    csv_path = args.csv
    if not os.path.exists(csv_path):
        print(f"❌ CSV file not found: {csv_path}")
        sys.exit(1)
    
    print(f"📊 Reading CSV file: {csv_path}")
    print(f"📁 Image folder: {IMAGE_FOLDER}")
    print(f"🌐 API Base URL: {BASE_URL}")
    
    # Read CSV file
    records = []
    with open(csv_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        records = list(reader)
    
    total_records = len(records)
    print(f"📋 Total records in CSV: {total_records}")
    
    # Apply start and limit
    start_idx = args.start
    if args.limit:
        end_idx = min(start_idx + args.limit, total_records)
    else:
        end_idx = total_records
    
    records_to_process = records[start_idx:end_idx]
    print(f"🎯 Processing records {start_idx} to {end_idx-1} ({len(records_to_process)} records)")
    
    # Process records
    success_count = 0
    skip_count = 0
    error_count = 0
    
    for i, record in enumerate(records_to_process, start=start_idx):
        print(f"\n{'='*60}")
        print(f"📝 Record {i+1}/{total_records}")
        
        success, reason = process_user_avatar(record)
        
        if success:
            if "already exists" in reason:
                skip_count += 1
            else:
                success_count += 1
        else:
            error_count += 1
            print(f"❌ Failed: {reason}")
        
        # Rate limiting - 0.5 second delay
        if i < end_idx - 1:  # Don't delay after the last record
            print("⏱️  Waiting 0.5 seconds...")
            time.sleep(0.5)
    
    # Final summary
    print(f"\n{'='*60}")
    print("📊 FINAL SUMMARY")
    print(f"✅ Successfully uploaded: {success_count}")
    print(f"⏭️  Skipped (already have avatar): {skip_count}")
    print(f"❌ Failed: {error_count}")
    print(f"📋 Total processed: {len(records_to_process)}")
    
    success_rate = ((success_count + skip_count) / len(records_to_process)) * 100 if records_to_process else 0
    print(f"📈 Success rate: {success_rate:.1f}%")

if __name__ == "__main__":
    main()
