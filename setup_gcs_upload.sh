#!/bin/bash

echo "🚀 Setting up Google Cloud Storage upload environment"
echo "=================================================="

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo "❌ Google Cloud CLI (gcloud) is not installed."
    echo "Please install it from: https://cloud.google.com/sdk/docs/install"
    exit 1
fi

echo "✅ Google Cloud CLI found"

# Check if authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n 1 > /dev/null; then
    echo "🔐 Setting up authentication..."
    echo "Please follow the authentication prompts:"
    gcloud auth login
    gcloud auth application-default login
else
    echo "✅ Already authenticated with Google Cloud"
fi

# Set project (optional - you can modify this)
echo "🔧 Setting up project configuration..."
gcloud config set project comfyui-data-analytic-project-424703

# Install Python dependencies
echo "📦 Installing Python dependencies..."
pip3 install -r gcs_requirements.txt

echo ""
echo "✅ Setup complete! You can now run:"
echo "   python3 upload_to_gcs_common.py"
echo ""
echo "📋 What this script will do:"
echo "   • Upload all images from ./files/profile_pic_processed/"
echo "   • To: gs://comfyui-data-analytic-project-424703/comfyui/common/"
echo "   • Preserve original filenames"
echo "   • Skip files that already exist"
echo "   • Provide detailed progress and summary"
