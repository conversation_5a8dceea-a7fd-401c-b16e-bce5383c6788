version: '3.8'

services:
  nginx:
    image: nginx:alpine
    container_name: cms_nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    restart: unless-stopped

  postgres:
    image: postgres:15
    container_name: cms_postgres
    environment:
      POSTGRES_DB: directus
      POSTGRES_USER: directus
      POSTGRES_PASSWORD: directus_password
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
      - ./backups:/backups
    ports:
      - "5432:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U directus"]
      interval: 30s
      timeout: 10s
      retries: 5

  directus:
    image: directus/directus:latest
    container_name: cms_directus
    ports:
      - "8055:8055"
    volumes:
      - ./gcp-credentials.json:/app/gcp-credentials.json:ro
      - ./data/directus_uploads:/directus/uploads
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      # Database Configuration
      DB_CLIENT: pg
      DB_HOST: postgres
      DB_PORT: 5432
      DB_DATABASE: directus
      DB_USER: directus
      DB_PASSWORD: directus_password
      
      # Directus Configuration
      SECRET: 'replace-d8f7e6c5b4a3928170'
      ADMIN_EMAIL: <EMAIL>
      ADMIN_PASSWORD: 'abc827sQzdos19'
      ACCESS_TOKEN_TTL: 30d
      REFRESH_TOKEN_TTL: 90d
      
      # Google Cloud Storage Configuration
      STORAGE_LOCATIONS: gcs
      STORAGE_GCS_DRIVER: gcs
      STORAGE_GCS_PROJECT_ID: data-analytic-project-424703
      STORAGE_GCS_KEY_FILENAME: /app/gcp-credentials.json
      STORAGE_GCS_BUCKET: comfyui-data-analytic-project-424703
      STORAGE_GCS_ROOT: comfyui/common
      
      # Public URL Configuration (adjust as needed)
      PUBLIC_URL: http://cms.copula.site
      
      # File Upload Configuration
      FILES_MAX_UPLOAD_SIZE: 100MB
      
      # Cache
      CACHE_ENABLED: false
      
      # Rate Limiting
      RATE_LIMITER_ENABLED: true
      RATE_LIMITER_POINTS: 25
      RATE_LIMITER_DURATION: 1
      
      # Logging
      LOG_LEVEL: info
      LOG_STYLE: pretty
      
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8055/server/health"]
      interval: 30s
      timeout: 10s
      retries: 5

  postgres_backup:
    image: postgres:15
    container_name: cms_postgres_backup
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./backups:/backups
      - ./backup-script.sh:/backup-script.sh:ro
    environment:
      PGHOST: postgres
      PGPORT: 5432
      PGDATABASE: directus
      PGUSER: directus
      PGPASSWORD: directus_password
    command: >
      sh -c "
        echo '0 2 * * * /backup-script.sh' | crontab - &&
        crond -f
      "
    restart: unless-stopped



networks:
  default:
    name: cms_network
