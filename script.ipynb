{"cells": [{"cell_type": "code", "execution_count": 3, "id": "2f8cfacc", "metadata": {}, "outputs": [], "source": ["personality = ['Imaginative', 'Organised', 'Outgoing', 'Warm', 'Sensitive', 'Inquisitive', 'Adaptable', 'Friendly', 'Considerate', 'Perceptive', 'Conventional', 'Spontaneous', 'Reserved', 'Independent', 'Calm']"]}, {"cell_type": "code", "execution_count": 4, "id": "95fc40e6", "metadata": {}, "outputs": [], "source": ["import re\n", "def name_to_code(name):\n", "    \"\"\"\n", "    Convert a hobby name to a code format (uppercase with underscores).\n", "    \n", "    Args:\n", "        name (str): The hobby name\n", "        \n", "    Returns:\n", "        str: The code format (e.g., \"Open-minded\" -> \"OPEN_MINDED\")\n", "    \"\"\"\n", "    # Remove special characters and replace spaces/hyphens with underscores\n", "    code = re.sub(r'[^\\w\\s-]', '', name)\n", "    # Replace spaces and hyphens with underscores\n", "    code = re.sub(r'[\\s-]+', '_', code)\n", "    # Convert to uppercase\n", "    code = code.upper()\n", "    # Remove leading/trailing underscores\n", "    code = code.strip('_')\n", "    return code\n", "\n"]}, {"cell_type": "code", "execution_count": 5, "id": "dce9096c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'name': 'Imaginative', 'code': 'IMAGINATIVE'}, {'name': 'Organised', 'code': 'ORGANISED'}, {'name': 'Outgoing', 'code': 'OUTGOING'}, {'name': 'Warm', 'code': 'WARM'}, {'name': 'Sensitive', 'code': 'SENSITIVE'}, {'name': 'Inquisitive', 'code': 'INQUISITIVE'}, {'name': 'Adaptable', 'code': 'ADAPTABLE'}, {'name': 'Friendly', 'code': 'FRIENDLY'}, {'name': 'Considerate', 'code': 'CONSIDERATE'}, {'name': 'Perceptive', 'code': 'PERCEPTIVE'}, {'name': 'Conventional', 'code': 'CONVENTIONAL'}, {'name': 'Spontaneous', 'code': 'SPONTANEOUS'}, {'name': 'Reserved', 'code': 'RESERVED'}, {'name': 'Independent', 'code': 'INDEPENDENT'}, {'name': 'Calm', 'code': 'CALM'}]\n"]}], "source": ["res = []\n", "for x in personality:\n", "    res.append(\n", "        {\"name\": x, \"code\": name_to_code(x)}\n", "    )\n", "    \n", "print(res)\n"]}, {"cell_type": "code", "execution_count": 8, "id": "588f38c5", "metadata": {}, "outputs": [{"data": {"text/plain": ["'[{\"name\": \"Imaginative\", \"code\": \"IMAGINATIVE\"}, {\"name\": \"Organised\", \"code\": \"ORGANISED\"}, {\"name\": \"Outgoing\", \"code\": \"OUTGOING\"}, {\"name\": \"Warm\", \"code\": \"WARM\"}, {\"name\": \"Sensitive\", \"code\": \"SENSITIVE\"}, {\"name\": \"Inquisitive\", \"code\": \"INQUISITIVE\"}, {\"name\": \"Adaptable\", \"code\": \"ADAPTABLE\"}, {\"name\": \"Friendly\", \"code\": \"FRIENDLY\"}, {\"name\": \"Considerate\", \"code\": \"CONSIDERATE\"}, {\"name\": \"Perceptive\", \"code\": \"PERCEPTIVE\"}, {\"name\": \"Conventional\", \"code\": \"CONVENTIONAL\"}, {\"name\": \"Spontaneous\", \"code\": \"SPONTANEOUS\"}, {\"name\": \"Reserved\", \"code\": \"RESERVED\"}, {\"name\": \"Independent\", \"code\": \"INDEPENDENT\"}, {\"name\": \"<PERSON>m\", \"code\": \"CALM\"}]'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["import json\n", "x = json.dumps(res)\n", "x"]}, {"cell_type": "code", "execution_count": 1, "id": "2725c9d2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Column: age\n", "Distinct values: [22 38 23 29 37 35 28 24 33 31 27 30 20 25 40 36 26 21 34 32 43 39 46 41\n", " 45 50 44 48 42 49]\n", "Number of distinct values: 30\n", "--------------------------------------------------\n", "Column: status\n", "Distinct values: ['single' 'available']\n", "Number of distinct values: 2\n", "--------------------------------------------------\n", "Column: gender\n", "Distinct values: ['m' 'f']\n", "Number of distinct values: 2\n", "--------------------------------------------------\n", "Column: body_type\n", "Distinct values: ['a little extra' 'thin' 'athletic' 'average' nan 'fit' 'curvy' 'skinny'\n", " 'full figured' 'jacked' 'used up' 'rather not say' 'overweight']\n", "Number of distinct values: 12\n", "--------------------------------------------------\n", "Column: diet\n", "Distinct values: ['strictly anything' 'anything' 'vegetarian' nan 'mostly anything'\n", " 'mostly vegetarian' 'strictly vegan' 'strictly vegetarian' 'mostly other'\n", " 'strictly other' 'vegan' 'mostly kosher' 'mostly vegan' 'other'\n", " 'strictly halal' 'mostly halal']\n", "Number of distinct values: 15\n", "--------------------------------------------------\n", "Column: drinks\n", "Distinct values: ['socially' 'not at all' 'often' 'rarely' nan 'very often' 'desperately']\n", "Number of distinct values: 6\n", "--------------------------------------------------\n", "Column: education\n", "Distinct values: ['college/diploma' 'master degree' 'bachelor degree' nan\n", " 'high school degree' 'phd or above']\n", "Number of distinct values: 5\n", "--------------------------------------------------\n", "Column: income\n", "Distinct values: [     -1   20000   40000   60000 1000000  150000   50000  100000   80000\n", "  500000   70000   30000  250000]\n", "Number of distinct values: 13\n", "--------------------------------------------------\n", "Column: job\n", "Distinct values: ['transportation' nan 'student' 'artistic / musical / writer'\n", " 'banking / financial / real estate' 'entertainment / media'\n", " 'medicine / health' 'sales / marketing / biz dev' 'education / academia'\n", " 'other' 'science / tech / engineering' 'computer / hardware / software'\n", " 'hospitality / travel' 'construction / craftsmanship'\n", " 'law / legal services' 'rather not say' 'executive / management'\n", " 'unemployed' 'clerical / administrative' 'political / government'\n", " 'military' 'retired']\n", "Number of distinct values: 21\n", "--------------------------------------------------\n", "Column: location\n", "Distinct values: ['south san francisco, california' 'san francisco, california'\n", " 'berkeley, california' 'san mateo, california' 'daly city, california'\n", " 'oakland, california' 'atherton, california' 'san leandro, california'\n", " 'san rafael, california' 'san jose, california' 'palo alto, california'\n", " 'belmont, california' 'emeryville, california' 'el granada, california'\n", " 'castro valley, california' 'fairfax, california'\n", " 'menlo park, california' 'burlingame, california' 'martinez, california'\n", " 'pleasant hill, california' 'vallejo, california'\n", " 'el cerrito, california' 'el sobrante, california'\n", " 'walnut creek, california' 'mill valley, california'\n", " 'hayward, california' 'alameda, california' 'stanford, california'\n", " 'san pablo, california' 'novato, california' 'pacifica, california'\n", " 'redwood city, california' 'mountain view, california'\n", " 'hercules, california' 'santa cruz, california' 'bolinas, california'\n", " 'san lorenzo, california' 'san bruno, california' 'millbrae, california'\n", " 'petaluma, california' 'pinole, california' 'san geronimo, california'\n", " 'san anselmo, california' 'benicia, california' 'fremont, california'\n", " 'brisbane, california' 'freedom, california' 'orinda, california'\n", " 'lafayette, california' 'san carlos, california' 'montara, california'\n", " 'crockett, california' 'belvedere tiburon, california'\n", " 'woodside, california' 'half moon bay, california' 'richmond, california'\n", " 'green brae, california' 'ross, california' 'albany, california'\n", " 'east palo alto, california' 'corte madera, california'\n", " 'sausalito, california' 'foster city, california' 'rodeo, california'\n", " 'los angeles, california' 'moraga, california' 'west oakland, california']\n", "Number of distinct values: 67\n", "--------------------------------------------------\n", "Column: family_plan\n", "Distinct values: [\"doesn't have kids, but might want them\" nan \"doesn't want kids\"\n", " \"doesn't have kids\" \"doesn't have kids, but wants them\" 'has a kid'\n", " 'has kids' \"doesn't have kids, and doesn't want any\"\n", " 'has a kid, and wants more' \"has a kid, but doesn't want more\"\n", " 'might want kids' 'wants kids' 'has kids, and might want more'\n", " \"has kids, but doesn't want more\" 'has a kid, and might want more'\n", " 'has kids, and wants more']\n", "Number of distinct values: 15\n", "--------------------------------------------------\n", "Column: pets\n", "Distinct values: ['likes dogs and likes cats' 'has cats' 'likes cats' nan\n", " 'likes dogs and dislikes cats' 'has dogs' 'has dogs and dislikes cats'\n", " 'likes dogs' 'has dogs and likes cats' 'likes dogs and has cats'\n", " 'dislikes dogs and has cats' 'has dogs and has cats'\n", " 'dislikes dogs and dislikes cats' 'dislikes cats'\n", " 'dislikes dogs and likes cats' 'dislikes dogs']\n", "Number of distinct values: 15\n", "--------------------------------------------------\n", "Column: smokes\n", "Distinct values: ['sometimes' 'no' 'yes' nan 'trying to quit' 'when drinking']\n", "Number of distinct values: 5\n", "--------------------------------------------------\n", "Column: essay0\n", "Distinct values: [\"about me:  i would love to think that i was some some kind of intellectual: either the dumbest smart guy, or the smartest dumb guy. can't say i can tell the difference. i love to talk about ideas and concepts. i forge odd metaphors instead of reciting cliches. like the simularities between a friend of mine's house and an underwater salt mine. my favorite word is salt by the way (weird choice i know). to me most things in life are better as metaphors. i seek to make myself a little better everyday, in some productively lazy way. got tired of tying my shoes. considered hiring a five year old, but would probably have to tie both of our shoes... decided to only wear leather shoes dress shoes.  about you:  you love to have really serious, really deep conversations about really silly stuff. you have to be willing to snap me out of a light hearted rant with a kiss. you don't have to be funny, but you have to be able to make me laugh. you should be able to bend spoons with your mind, and telepathically make me smile while i am still at work. you should love life, and be cool with just letting the wind blow. extra points for reading all this and guessing my favorite video game (no hints given yet). and lastly you have a good attention span.\"\n", " \"i'm not ashamed of much, but writing public text on an online dating site makes me pleasantly uncomfortable. i'll try to be as earnest as possible in the noble endeavor of standing naked before the world.  i've lived in san francisco for 15 years, and both love it and find myself frustrated with its deficits. lots of great friends and acquaintances (which increases my apprehension to put anything on this site), but i'm feeling like meeting some new people that aren't just friends of friends. it's okay if you are a friend of a friend too. chances are, if you make it through the complex filtering process of multiple choice questions, lifestyle statistics, photo scanning, and these indulgent blurbs of text without moving quickly on to another search result, you are probably already a cultural peer and at most 2 people removed. at first, i thought i should say as little as possible here to avoid you, but that seems silly.  as far as culture goes, i'm definitely more on the weird side of the spectrum, but i don't exactly wear it on my sleeve. once you get me talking, it will probably become increasingly apparent that while i'd like to think of myself as just like everybody else (and by some definition i certainly am), most people don't see me that way. that's fine with me. most of the people i find myself gravitating towards are pretty weird themselves. you probably are too.\"\n", " 'i work in a library and go to school. . .' ...\n", " \"i tend to be creative in the way i go about life...  i love to be at home and 'domesticate'...i've painted every place i've ever lived in and tend to really adapt my spaces to my sensibilities. i'm completely in love with the cottage i'm in now.  i have a genuine fascination and love for life and people. friends and strangers are all really important to me. i put a lot of value in knowing people and am extremely loyal. i have a thirst for meeting strangers and am writing a book about my experiences with that. i have a genuine fascination and love for people...and life.  on the flip side i also love travel and going to new places. a lot of people say they've been to all the of the seven continents except antarctica and i've been to antarctica so now i figure i should probably go to all seven. i'm three down with four to go. for the last 4.5 years i've been letting my roots comfortably sink in sf as it is my favorite home base.  i can come off a little old fashion or adventurous and bold...quiet or talkative...it all depends at what moment you catch me. i had a friend who said i am the contemporary version of an old fashion lady.  my tendency is to look at things in as many ways as possible which has made me a bit diplomatic in the way i deal with people. i've been told i am calming. i enjoy good communication and long nights of story telling.  i've got a green thumb and have even hugged a few trees in my day. i'm extremely fascinated by insects and have been incorporating them into my drawings and paintings with increasing frequency. i love learning about new strange facts and how things work. i often bury my nose in a book or two. i grow a lot of plants. i meditate. i cook more meals than i eat out.  i enjoy life. i'm curious.\"\n", " \"i am a chinky pinay who has been 4x blessed as a proud mother of four beautiful kids. they are my heart, my motivation and salvation and a true reflection of what is the best in me.  i am an optimistic person who's always looking at the glass half full, even though that glass is hella dirty... lol.  i hold my family and the closest of my friends dear to my heart and without them, i wouldn't be able to overcome all the hurdles in my life.  i am a firm believer that things do happen for a reason but only over time, will those reasons reveal themselves. because of this, i have grown to be one of the most patient and passive people you'll ever know, but sometimes i feel like i'm on the verge of a nervous breakdown... just one of those moments you know?  but hey... i am a work in progress and i'll survive... with that said, i believe that god gives us trials in life to test us... not to punish us and in the end i'll find what all 31/4's are looking for.\"\n", " \"i love to travel, and go on road trips, it really helps to see the bigger picture in life, when we become sucked into work, and lifes dramma, stress, it's nice to lift your head above it all, and live a different, more freeing life for a few weeks at a time..and see beautiful things... i love photogrophy, and knowing the balance of partying, and being responsible. i am looking for someone like that as well, who knows how to have a good time,and to have crazy times fun with me, but can also take care of their own shit. i find it very attractive.  i am a cuddle whore, good with money, and cute\"]\n", "Number of distinct values: 3012\n", "--------------------------------------------------\n", "Column: file_name\n", "Distinct values: ['M_22_asian_11462.png' 'M_38_asian_12327.png' 'M_23_asian_12872.png' ...\n", " 'F_30_asian_2104.png' 'F_30_asian_2645.png' 'F_30_asian_47371.png']\n", "Number of distinct values: 3013\n", "--------------------------------------------------\n", "Column: openness\n", "Distinct values: [ 5  4  6  8 10  7  1  3  2  9]\n", "Number of distinct values: 10\n", "--------------------------------------------------\n", "Column: conscientiousness\n", "Distinct values: [ 1  3  5  8  9  7  6  2  4 10]\n", "Number of distinct values: 10\n", "--------------------------------------------------\n", "Column: extraversion\n", "Distinct values: [ 8  9  4  1  2 10  7  6  5  3]\n", "Number of distinct values: 10\n", "--------------------------------------------------\n", "Column: agreeableness\n", "Distinct values: [10  8  2  6  7  3  9  5  4  1]\n", "Number of distinct values: 10\n", "--------------------------------------------------\n", "Column: neuroticism\n", "Distinct values: [ 5 10  3  2  6  1  4  9  8  7]\n", "Number of distinct values: 10\n", "--------------------------------------------------\n", "Column: personality_tags\n", "Distinct values: ['Inquisitive, Spontaneous, Outgoing, Warm, Perceptive'\n", " 'Inquisitive, Spontaneous, Outgoing, Warm, Sensitive'\n", " 'Inquisitive, Spontaneous, Friendly, Independent, Calm'\n", " 'Imaginative, Spontaneous, Reserved, Considerate, Calm'\n", " 'Imaginative, Adaptable, Reserved, Considerate, Perceptive'\n", " 'Inquisitive, Organised, Outgoing, Independent, Perceptive'\n", " 'Conventional, Organised, Outgoing, Independent, Calm'\n", " 'Imaginative, Adaptable, Reserved, Warm, Perceptive'\n", " 'Conventional, Organised, Outgoing, Considerate, Sensitive'\n", " 'Conventional, Adaptable, Friendly, Independent, Sensitive'\n", " 'Conventional, Adaptable, Outgoing, Considerate, Perceptive'\n", " 'Imaginative, Adaptable, Friendly, Considerate, Sensitive'\n", " 'Conventional, Spontaneous, Friendly, Warm, Perceptive'\n", " 'Inquisitive, Spontaneous, Friendly, Considerate, Sensitive'\n", " 'Imaginative, Spontaneous, Reserved, Warm, Sensitive'\n", " 'Conventional, Organised, Outgoing, Independent, Sensitive'\n", " 'Imaginative, Spontaneous, Friendly, Considerate, Calm'\n", " 'Inquisitive, Spontaneous, Friendly, Considerate, Perceptive'\n", " 'Imaginative, Adaptable, Outgoing, Independent, Perceptive'\n", " 'Inquisitive, Spontaneous, Reserved, Considerate, Calm'\n", " 'Inquisitive, Organised, Friendly, Warm, Sensitive'\n", " 'Conventional, Organised, Friendly, Independent, Perceptive'\n", " 'Inquisitive, Adaptable, Friendly, Considerate, Sensitive'\n", " 'Conventional, Organised, Friendly, Warm, Perceptive'\n", " 'Conventional, Organised, Reserved, Considerate, Sensitive'\n", " 'Inquisitive, Adaptable, Reserved, Considerate, Calm'\n", " 'Inquisitive, Adaptable, Outgoing, Warm, Perceptive'\n", " 'Conventional, Adaptable, Friendly, Considerate, Sensitive'\n", " 'Inquisitive, Spontaneous, Reserved, Independent, Sensitive'\n", " 'Inquisitive, Adaptable, Outgoing, Considerate, Sensitive'\n", " 'Inquisitive, Organised, Friendly, Warm, Perceptive'\n", " 'Inquisitive, Organised, Reserved, Considerate, Perceptive'\n", " 'Imaginative, Adaptable, Friendly, Warm, Perceptive'\n", " 'Inquisitive, Spontaneous, Friendly, Warm, Sensitive'\n", " 'Conventional, Adaptable, Reserved, Warm, Sensitive'\n", " 'Inquisitive, Spontaneous, Reserved, Warm, Calm'\n", " 'Conventional, Spontaneous, Reserved, Independent, Sensitive'\n", " 'Imaginative, Adaptable, Reserved, Considerate, Calm'\n", " 'Imaginative, Spontaneous, Friendly, Independent, Calm'\n", " 'Conventional, Adaptable, Outgoing, Warm, Perceptive'\n", " 'Inquisitive, Spontaneous, Reserved, Considerate, Perceptive'\n", " 'Inquisitive, Spontaneous, Friendly, Warm, Perceptive'\n", " 'Imaginative, Spontaneous, Reserved, Considerate, Perceptive'\n", " 'Conventional, Adaptable, Outgoing, Independent, Calm'\n", " 'Imaginative, Adaptable, Outgoing, Considerate, Calm'\n", " 'Inquisitive, Adaptable, Outgoing, Considerate, Perceptive'\n", " 'Inquisitive, Adaptable, Friendly, Considerate, Perceptive'\n", " 'Inquisitive, Adaptable, Friendly, Warm, Perceptive'\n", " 'Conventional, Spontaneous, Friendly, Considerate, Perceptive'\n", " 'Imaginative, Organised, Outgoing, Warm, Perceptive'\n", " 'Imaginative, Spontaneous, Friendly, Independent, Perceptive'\n", " 'Inquisitive, Spontaneous, Outgoing, Independent, Perceptive'\n", " 'Imaginative, Organised, Friendly, Warm, Perceptive'\n", " 'Inquisitive, Spontaneous, Outgoing, Considerate, Perceptive'\n", " 'Imaginative, Organised, Reserved, Warm, Calm'\n", " 'Inquisitive, Organised, Outgoing, Considerate, Sensitive'\n", " 'Imaginative, Organised, Outgoing, Warm, Sensitive'\n", " 'Inquisitive, Adaptable, Outgoing, Independent, Sensitive'\n", " 'Conventional, Adaptable, Outgoing, Considerate, Calm'\n", " 'Conventional, Adaptable, Outgoing, Considerate, Sensitive'\n", " 'Inquisitive, Spontaneous, Reserved, Warm, Perceptive'\n", " 'Inquisitive, Spontaneous, Reserved, Warm, Sensitive'\n", " 'Inquisitive, Spontaneous, Friendly, Independent, Sensitive'\n", " 'Inquisitive, Organised, Reserved, Independent, Sensitive'\n", " 'Inquisitive, Spontaneous, Reserved, Considerate, Sensitive'\n", " 'Inquisitive, Organised, Outgoing, Independent, Calm'\n", " 'Imaginative, Spontaneous, Friendly, Warm, Sensitive'\n", " 'Imaginative, Adaptable, Reserved, Independent, Calm'\n", " 'Inquisitive, Adaptable, Friendly, Independent, Calm'\n", " 'Inquisitive, Organised, Friendly, Warm, Calm'\n", " 'Inquisitive, Adaptable, Reserved, Considerate, Perceptive'\n", " 'Inquisitive, Adaptable, Reserved, Considerate, Sensitive'\n", " 'Inquisitive, Adaptable, Outgoing, Warm, Sensitive'\n", " 'Imaginative, Spontaneous, Reserved, Considerate, Sensitive'\n", " 'Imaginative, Adaptable, Outgoing, Warm, Sensitive'\n", " 'Inquisitive, Adaptable, Outgoing, Independent, Perceptive'\n", " 'Inquisitive, Organised, Outgoing, Warm, Sensitive'\n", " 'Inquisitive, Spontaneous, Outgoing, Considerate, Calm'\n", " 'Imaginative, Adaptable, Outgoing, Independent, Calm'\n", " 'Conventional, Adaptable, Friendly, Considerate, Perceptive'\n", " 'Inquisitive, Spontaneous, Reserved, Independent, Calm'\n", " 'Imaginative, Spontaneous, Outgoing, Warm, Sensitive'\n", " 'Conventional, Spontaneous, Reserved, Considerate, Calm'\n", " 'Conventional, Organised, Reserved, Warm, Calm'\n", " 'Imaginative, Spontaneous, Outgoing, Considerate, Sensitive'\n", " 'Imaginative, Spontaneous, Outgoing, Considerate, Perceptive'\n", " 'Inquisitive, Adaptable, Friendly, Independent, Sensitive'\n", " 'Inquisitive, Spontaneous, Friendly, Considerate, Calm'\n", " 'Conventional, Adaptable, Outgoing, Warm, Calm'\n", " 'Conventional, Spontaneous, Reserved, Warm, Calm'\n", " 'Conventional, Spontaneous, Outgoing, Considerate, Calm'\n", " 'Inquisitive, Adaptable, Reserved, Warm, Perceptive'\n", " 'Imaginative, Spontaneous, Outgoing, Independent, Calm'\n", " 'Conventional, Organised, Outgoing, Considerate, Perceptive'\n", " 'Inquisitive, Organised, Reserved, Warm, Perceptive'\n", " 'Imaginative, Adaptable, Friendly, Considerate, Perceptive'\n", " 'Conventional, Adaptable, Friendly, Considerate, Calm'\n", " 'Conventional, Adaptable, Reserved, Considerate, Calm'\n", " 'Conventional, Organised, Friendly, Considerate, Calm'\n", " 'Imaginative, Organised, Reserved, Warm, Sensitive'\n", " 'Conventional, Organised, Friendly, Considerate, Sensitive'\n", " 'Imaginative, Organised, Friendly, Independent, Perceptive'\n", " 'Imaginative, Organised, Friendly, Considerate, Sensitive'\n", " 'Imaginative, Organised, Friendly, Considerate, Perceptive'\n", " 'Inquisitive, Organised, Reserved, Considerate, Calm'\n", " 'Inquisitive, Organised, Friendly, Independent, Perceptive'\n", " 'Inquisitive, Adaptable, Reserved, Independent, Sensitive'\n", " 'Imaginative, Organised, Friendly, Considerate, Calm'\n", " 'Inquisitive, Organised, Outgoing, Considerate, Calm'\n", " 'Inquisitive, Organised, Outgoing, Warm, Calm'\n", " 'Conventional, Adaptable, Friendly, Warm, Perceptive'\n", " 'Imaginative, Adaptable, Friendly, Independent, Calm'\n", " 'Imaginative, Spontaneous, Friendly, Warm, Calm'\n", " 'Conventional, Organised, Friendly, Warm, Calm'\n", " 'Imaginative, Adaptable, Reserved, Considerate, Sensitive'\n", " 'Conventional, Adaptable, Friendly, Independent, Calm'\n", " 'Conventional, Organised, Outgoing, Warm, Perceptive'\n", " 'Conventional, Adaptable, Friendly, Warm, Sensitive'\n", " 'Imaginative, Spontaneous, Outgoing, Considerate, Calm'\n", " 'Inquisitive, Organised, Reserved, Independent, Perceptive'\n", " 'Imaginative, Adaptable, Friendly, Warm, Sensitive'\n", " 'Imaginative, Organised, Outgoing, Considerate, Calm'\n", " 'Inquisitive, Spontaneous, Reserved, Independent, Perceptive'\n", " 'Imaginative, Adaptable, Friendly, Warm, Calm'\n", " 'Inquisitive, Adaptable, Reserved, Warm, Calm'\n", " 'Conventional, Organised, Reserved, Independent, Sensitive'\n", " 'Inquisitive, Organised, Friendly, Considerate, Calm'\n", " 'Imaginative, Spontaneous, Outgoing, Warm, Perceptive'\n", " 'Inquisitive, Organised, Reserved, Warm, Calm'\n", " 'Inquisitive, Spontaneous, Outgoing, Independent, Calm'\n", " 'Imaginative, Adaptable, Outgoing, Considerate, Perceptive'\n", " 'Imaginative, Spontaneous, Outgoing, Independent, Sensitive'\n", " 'Inquisitive, Organised, Reserved, Independent, Calm'\n", " 'Inquisitive, Organised, Friendly, Considerate, Perceptive'\n", " 'Conventional, Spontaneous, Outgoing, Warm, Calm'\n", " 'Imaginative, Organised, Outgoing, Independent, Perceptive'\n", " 'Conventional, Spontaneous, Outgoing, Considerate, Perceptive'\n", " 'Imaginative, Organised, Friendly, Warm, Sensitive'\n", " 'Imaginative, Organised, Reserved, Independent, Perceptive'\n", " 'Inquisitive, Organised, Friendly, Independent, Sensitive'\n", " 'Imaginative, Spontaneous, Reserved, Warm, Calm'\n", " 'Imaginative, Spontaneous, Friendly, Considerate, Perceptive'\n", " 'Inquisitive, Adaptable, Reserved, Independent, Calm'\n", " 'Inquisitive, Adaptable, Friendly, Considerate, Calm'\n", " 'Imaginative, Adaptable, Reserved, Warm, Calm'\n", " 'Conventional, Organised, Friendly, Independent, Calm'\n", " 'Imaginative, Adaptable, Friendly, Considerate, Calm'\n", " 'Conventional, Spontaneous, Outgoing, Independent, Sensitive'\n", " 'Conventional, Organised, Reserved, Considerate, Perceptive'\n", " 'Imaginative, Adaptable, Friendly, Independent, Sensitive'\n", " 'Inquisitive, Adaptable, Reserved, Warm, Sensitive'\n", " 'Conventional, Adaptable, Reserved, Considerate, Perceptive'\n", " 'Conventional, Spontaneous, Friendly, Independent, Calm'\n", " 'Imaginative, Spontaneous, Reserved, Independent, Perceptive'\n", " 'Imaginative, Organised, Outgoing, Considerate, Perceptive'\n", " 'Conventional, Spontaneous, Reserved, Independent, Calm'\n", " 'Imaginative, Organised, Reserved, Considerate, Calm'\n", " 'Imaginative, Spontaneous, Friendly, Independent, Sensitive'\n", " 'Inquisitive, Adaptable, Outgoing, Considerate, Calm'\n", " 'Imaginative, Adaptable, Friendly, Independent, Perceptive'\n", " 'Imaginative, Organised, Outgoing, Independent, Calm'\n", " 'Imaginative, Spontaneous, Friendly, Considerate, Sensitive'\n", " 'Inquisitive, Organised, Reserved, Considerate, Sensitive'\n", " 'Conventional, Adaptable, Friendly, Independent, Perceptive'\n", " 'Imaginative, Organised, Reserved, Independent, Calm'\n", " 'Imaginative, Spontaneous, Outgoing, Warm, Calm'\n", " 'Conventional, Organised, Outgoing, Considerate, Calm'\n", " 'Inquisitive, Organised, Reserved, Warm, Sensitive'\n", " 'Inquisitive, Adaptable, Outgoing, Independent, Calm'\n", " 'Inquisitive, Organised, Outgoing, Warm, Perceptive'\n", " 'Conventional, Spontaneous, Reserved, Considerate, Perceptive'\n", " 'Imaginative, Spontaneous, Friendly, Warm, Perceptive'\n", " 'Inquisitive, Adaptable, Friendly, Warm, Sensitive'\n", " 'Conventional, Adaptable, Reserved, Warm, Calm'\n", " 'Conventional, Adaptable, Reserved, Independent, Sensitive'\n", " 'Imaginative, Adaptable, Outgoing, Considerate, Sensitive'\n", " 'Inquisitive, Adaptable, Reserved, Independent, Perceptive'\n", " 'Conventional, Spontaneous, Friendly, Considerate, Sensitive'\n", " 'Inquisitive, Spontaneous, Outgoing, Warm, Calm'\n", " 'Inquisitive, Spontaneous, Outgoing, Independent, Sensitive'\n", " 'Conventional, Organised, Reserved, Independent, Perceptive'\n", " 'Conventional, Spontaneous, Outgoing, Independent, Perceptive'\n", " 'Imaginative, Organised, Friendly, Independent, Calm'\n", " 'Conventional, Adaptable, Reserved, Warm, Perceptive'\n", " 'Conventional, Spontaneous, Outgoing, Warm, Perceptive'\n", " 'Conventional, Adaptable, Outgoing, Independent, Perceptive'\n", " 'Imaginative, Adaptable, Reserved, Independent, Perceptive'\n", " 'Imaginative, Organised, Reserved, Warm, Perceptive'\n", " 'Conventional, Spontaneous, Reserved, Considerate, Sensitive'\n", " 'Conventional, Spontaneous, Outgoing, Independent, Calm'\n", " 'Inquisitive, Spontaneous, Outgoing, Considerate, Sensitive'\n", " 'Conventional, Organised, Friendly, Considerate, Perceptive'\n", " 'Imaginative, Spontaneous, Reserved, Independent, Calm'\n", " 'Conventional, Organised, Reserved, Independent, Calm'\n", " 'Conventional, Spontaneous, Friendly, Independent, Perceptive'\n", " 'Conventional, Adaptable, Outgoing, Independent, Sensitive'\n", " 'Inquisitive, Organised, Outgoing, Considerate, Perceptive'\n", " 'Inquisitive, Adaptable, Friendly, Warm, Calm'\n", " 'Inquisitive, Spontaneous, Friendly, Independent, Perceptive'\n", " 'Conventional, Spontaneous, Reserved, Warm, Perceptive'\n", " 'Imaginative, Organised, Reserved, Considerate, Sensitive'\n", " 'Conventional, Organised, Friendly, Warm, Sensitive'\n", " 'Imaginative, Organised, Outgoing, Warm, Calm'\n", " 'Inquisitive, Organised, Friendly, Independent, Calm'\n", " 'Imaginative, Organised, Outgoing, Considerate, Sensitive'\n", " 'Imaginative, Organised, Friendly, Warm, Calm'\n", " 'Inquisitive, Organised, Friendly, Considerate, Sensitive'\n", " 'Conventional, Spontaneous, Friendly, Warm, Calm'\n", " 'Imaginative, Organised, Reserved, Considerate, Perceptive'\n", " 'Inquisitive, Adaptable, Friendly, Independent, Perceptive'\n", " 'Conventional, Adaptable, Friendly, Warm, Calm'\n", " 'Conventional, Organised, Reserved, Considerate, Calm'\n", " 'Inquisitive, Spontaneous, Friendly, Warm, Calm'\n", " 'Conventional, Spontaneous, Reserved, Independent, Perceptive'\n", " 'Conventional, Adaptable, Reserved, Independent, Calm'\n", " 'Conventional, Spontaneous, Friendly, Warm, Sensitive'\n", " 'Conventional, Organised, Reserved, Warm, Perceptive'\n", " 'Conventional, Organised, Friendly, Independent, Sensitive'\n", " 'Imaginative, Organised, Reserved, Independent, Sensitive'\n", " 'Conventional, Adaptable, Reserved, Considerate, Sensitive'\n", " 'Imaginative, Adaptable, Outgoing, Warm, Calm'\n", " 'Conventional, Adaptable, Outgoing, Warm, Sensitive'\n", " 'Conventional, Organised, Outgoing, Warm, Calm'\n", " 'Imaginative, Spontaneous, Outgoing, Independent, Perceptive'\n", " 'Conventional, Spontaneous, Friendly, Independent, Sensitive'\n", " 'Imaginative, Adaptable, Outgoing, Warm, Perceptive'\n", " 'Imaginative, Adaptable, Outgoing, Independent, Sensitive'\n", " 'Inquisitive, Adaptable, Outgoing, Warm, Calm'\n", " 'Conventional, Adaptable, Reserved, Independent, Perceptive'\n", " 'Imaginative, Adaptable, Reserved, Independent, Sensitive'\n", " 'Conventional, Spontaneous, Reserved, Warm, Sensitive'\n", " 'Conventional, Spontaneous, Friendly, Considerate, Calm'\n", " 'Conventional, Organised, Reserved, Warm, Sensitive'\n", " 'Conventional, Organised, Outgoing, Independent, Perceptive'\n", " 'Conventional, Spontaneous, Outgoing, Warm, Sensitive'\n", " 'Imaginative, Organised, Friendly, Independent, Sensitive'\n", " 'Imaginative, Spontaneous, Reserved, Warm, Perceptive'\n", " 'Imaginative, Organised, Outgoing, Independent, Sensitive'\n", " 'Imaginative, Adaptable, Reserved, Warm, Sensitive'\n", " 'Conventional, Spontaneous, Outgoing, Considerate, Sensitive'\n", " 'Imaginative, Spontaneous, Reserved, Independent, Sensitive'\n", " 'Inquisitive, Organised, Outgoing, Independent, Sensitive'\n", " 'Conventional, Organised, Outgoing, Warm, Sensitive']\n", "Number of distinct values: 243\n", "--------------------------------------------------\n", "Column: interests\n", "Distinct values: ['Camping, Shopping, Swimming, Netflix, Basketball'\n", " 'Puzzles, Aminals, Traveling, Hiking, Acting'\n", " 'Beach trips, Swimming, Shopping, Learning languages, Painting' ...\n", " 'Tennis, Film-making, Writing, Coffee enthusiast, Concerts'\n", " 'Beach trips, Astrology, Surfing, Basketball, Volunteering'\n", " 'Crafts, Cooking, Running, Gym, Rock climbing']\n", "Number of distinct values: 3013\n", "--------------------------------------------------\n", "Column: race\n", "Distinct values: ['asian' 'hispanic' 'white' 'black']\n", "Number of distinct values: 4\n", "--------------------------------------------------\n", "Column: id\n", "Distinct values: [11462 12327 12872 ...  2104  2645 47371]\n", "Number of distinct values: 3013\n", "--------------------------------------------------\n", "Column: image_source\n", "Distinct values: ['ffhq' 'other_apps' 'pexels']\n", "Number of distinct values: 3\n", "--------------------------------------------------\n", "Column: first_name\n", "Distinct values: ['<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON>' 'Susan'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON><PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON><PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON><PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON><PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON><PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON><PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON><PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON><PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' 'T<PERSON><PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON>'\n", " '<PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' 'April' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON><PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' 'Brandy'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' 'Ke<PERSON>'\n", " '<PERSON>' '<PERSON><PERSON><PERSON>' '<PERSON>' '<PERSON><PERSON><PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON><PERSON>' 'Autumn' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON><PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON><PERSON>' '<PERSON><PERSON><PERSON>'\n", " 'Tam<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' 'Jo<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON><PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON><PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON><PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON><PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON><PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON><PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON>'\n", " '<PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON><PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON><PERSON>' 'Jo<PERSON>'\n", " '<PERSON><PERSON><PERSON>' '<PERSON>' '<PERSON><PERSON><PERSON>' '<PERSON><PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON><PERSON>'\n", " '<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>' 'Jeanette' '<PERSON>' '<PERSON>'\n", " '<PERSON><PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>']\n", "Number of distinct values: 530\n", "--------------------------------------------------\n", "Column: last_name\n", "Distinct values: ['<PERSON>' '<PERSON>uyen' '<PERSON><PERSON>' '<PERSON>' '<PERSON><PERSON><PERSON>' 'Park' '<PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON>' 'Cho'\n", " 'Duong' 'Li' '<PERSON>' 'Song' 'Tran' 'Yoon' 'Han' '<PERSON>' '<PERSON>' 'Lu'\n", " 'Pacheco' 'Ho' '<PERSON><PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON>' 'Chu<PERSON>' '<PERSON><PERSON>' '<PERSON><PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' 'Dinh' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>ung' '<PERSON>' '<PERSON><PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>ung'\n", " '<PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON>' 'King' '<PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' 'Ba<PERSON>'\n", " '<PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>' 'R<PERSON>' 'Ph<PERSON>' '<PERSON>' 'Mcfarland' '<PERSON>'\n", " '<PERSON>' '<PERSON><PERSON>' '<PERSON><PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>' 'Ong'\n", " 'Wang' '<PERSON>' '<PERSON>' '<PERSON>' 'Gonzalez' 'Brown' 'Flores' 'Moss' 'Ang'\n", " 'Dang' '<PERSON>' '<PERSON>' 'Oh' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " 'Zava<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' 'Se<PERSON>' '<PERSON>' '<PERSON>' 'Gray'\n", " 'Hernandez' 'Morgan' 'Newton' 'Washington' 'Peterson' 'Rangel' 'Phillips'\n", " '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' 'Allison' 'Price'\n", " '<PERSON>' '<PERSON>' '<PERSON>' 'Norton' 'Collins' 'Pena' 'Acevedo' 'Sloan'\n", " 'Herr<PERSON>' '<PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>'\n", " '<PERSON>' '<PERSON><PERSON><PERSON>' '<PERSON><PERSON><PERSON><PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>' '<PERSON>']\n", "Number of distinct values: 147\n", "--------------------------------------------------\n", "Column: height_cm\n", "Distinct values: [190. 173. 180. 168. 165. 178. 183. 170. 163. 152. 175. 185. 157. 155.\n", " 188. 198. 160. 150. 193. 231. 211. 147. 196. 241. 142. 236.  20. 145.]\n", "Number of distinct values: 28\n", "--------------------------------------------------\n", "Column: desired_relationship\n", "Distinct values: ['New friends' 'Long-term relationship' 'Long-term, open to short-term'\n", " 'Short-term relationship' 'Short-term, open to long-term']\n", "Number of distinct values: 5\n", "--------------------------------------------------\n", "Column: orientation\n", "Distinct values: ['straight']\n", "Number of distinct values: 1\n", "--------------------------------------------------\n"]}], "source": ["import pandas as pd\n", "\n", "df = pd.read_csv(\"sample.csv\")\n", "\n", "for column in df.columns:\n", "    print(f\"Column: {column}\")\n", "    print(f\"Distinct values: {df[column].unique()}\")\n", "    print(f\"Number of distinct values: {df[column].nunique()}\")\n", "    print(\"-\" * 50)"]}, {"cell_type": "code", "execution_count": null, "id": "b2e29d47", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 5}