#!/usr/bin/env python3
"""
MIME Type Verification Script

This script verifies that uploaded avatar images have the correct MIME types.
It checks the file metadata in the CMS to ensure images are properly typed.

Usage:
    python3 verify_mime_types.py [--limit N] [--csv path]
"""

import argparse
import csv
import json
import os
import requests
import sys

# Configuration
BASE_URL = "https://cms.copula.site"
SESSION_TOKEN = "pcTMayE-BZ7kqVkF9Va6wlhIe5D5KVGO"
DEFAULT_CSV_PATH = "new_data.csv"

# Headers for API requests
HEADERS = {
    'accept': 'application/json, text/plain, */*',
    'Cookie': f'directus_session_token={SESSION_TOKEN}'
}

def get_user_profile_by_sample_id(sample_id):
    """
    Retrieve user profile by sample_id using the filter API
    """
    url = f"{BASE_URL}/items/UserProfile"
    params = {
        'filter[sample_id][_eq]': sample_id
    }
    
    try:
        response = requests.get(url, headers=HEADERS, params=params)
        response.raise_for_status()
        
        data = response.json()
        if data.get('data') and len(data['data']) > 0:
            return data['data'][0]
        else:
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error retrieving profile for sample_id {sample_id}: {e}")
        return None

def get_file_metadata(file_id):
    """
    Get file metadata including MIME type from the CMS
    """
    url = f"{BASE_URL}/files/{file_id}"
    
    try:
        response = requests.get(url, headers=HEADERS)
        response.raise_for_status()
        
        data = response.json()
        if data.get('data'):
            return data['data']
        else:
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error retrieving file metadata for {file_id}: {e}")
        return None

def verify_user_avatar_mime_type(csv_row):
    """
    Verify MIME type for a single user's avatar
    Returns: (has_avatar, correct_mime_type, file_info)
    """
    sample_id = csv_row['id']
    file_name = csv_row['file_name']
    first_name = csv_row['first_name']
    last_name = csv_row['last_name']
    
    # Get user profile
    profile = get_user_profile_by_sample_id(sample_id)
    if not profile:
        return False, False, f"Profile not found for {first_name} {last_name} (ID: {sample_id})"
    
    profile_id = profile['id']
    avatar_id = profile.get('avatar')
    
    profile_info = f"{first_name} {last_name} (ID: {sample_id}, Profile: {profile_id})"
    
    if not avatar_id:
        return False, False, f"{profile_info} - No avatar"
    
    # Get file metadata
    file_metadata = get_file_metadata(avatar_id)
    if not file_metadata:
        return True, False, f"{profile_info} - Avatar: {avatar_id} - Could not retrieve metadata"
    
    mime_type = file_metadata.get('type')
    file_size = file_metadata.get('filesize')
    filename = file_metadata.get('filename_download')
    
    # Check if MIME type is correct (should be image/*)
    is_correct_mime = mime_type and mime_type.startswith('image/')
    
    file_info = f"{profile_info} - Avatar: {avatar_id}"
    file_info += f"\n   📁 Filename: {filename}"
    file_info += f"\n   📄 MIME Type: {mime_type}"
    file_info += f"\n   📏 Size: {file_size} bytes"
    file_info += f"\n   📋 Expected file: {file_name}"
    
    return True, is_correct_mime, file_info

def main():
    parser = argparse.ArgumentParser(description='Verify MIME types of uploaded avatar images')
    parser.add_argument('--limit', type=int, default=10, help='Number of records to verify (default: 10)')
    parser.add_argument('--csv', default=DEFAULT_CSV_PATH, help='Path to CSV file')
    
    args = parser.parse_args()
    
    csv_path = args.csv
    if not os.path.exists(csv_path):
        print(f"❌ CSV file not found: {csv_path}")
        sys.exit(1)
    
    print(f"🔍 Verifying avatar MIME types...")
    print(f"📊 CSV file: {csv_path}")
    print(f"🌐 API Base URL: {BASE_URL}")
    print(f"📋 Checking first {args.limit} records")
    
    # Read CSV file
    records = []
    with open(csv_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        records = list(reader)
    
    total_records = len(records)
    records_to_check = records[:args.limit]
    
    print(f"\n{'='*80}")
    print("🔍 MIME TYPE VERIFICATION RESULTS")
    print(f"{'='*80}")
    
    has_avatar_count = 0
    correct_mime_count = 0
    incorrect_mime_count = 0
    no_avatar_count = 0
    error_count = 0
    
    for i, record in enumerate(records_to_check):
        print(f"\n📝 Record {i+1}:")
        
        try:
            has_avatar, correct_mime, file_info = verify_user_avatar_mime_type(record)
            
            if has_avatar:
                has_avatar_count += 1
                if correct_mime:
                    print(f"✅ {file_info}")
                    correct_mime_count += 1
                else:
                    print(f"❌ {file_info}")
                    print(f"   ⚠️  INCORRECT MIME TYPE!")
                    incorrect_mime_count += 1
            else:
                print(f"❌ {file_info}")
                no_avatar_count += 1
                
        except Exception as e:
            print(f"💥 Error checking record {i+1}: {e}")
            error_count += 1
    
    # Summary
    print(f"\n{'='*80}")
    print("📊 MIME TYPE VERIFICATION SUMMARY")
    print(f"{'='*80}")
    print(f"✅ Profiles with correct MIME types: {correct_mime_count}")
    print(f"❌ Profiles with incorrect MIME types: {incorrect_mime_count}")
    print(f"⚠️  Profiles without avatars: {no_avatar_count}")
    print(f"💥 Errors: {error_count}")
    print(f"📋 Total checked: {len(records_to_check)}")
    
    if has_avatar_count > 0:
        correct_rate = (correct_mime_count / has_avatar_count) * 100
        print(f"📈 Correct MIME type rate: {correct_rate:.1f}%")
    
    if incorrect_mime_count > 0:
        print(f"\n⚠️  {incorrect_mime_count} profiles have incorrect MIME types!")
        print("💡 Run the reupload_avatars.py script to fix MIME types.")
    else:
        print(f"\n🎉 All avatars have correct MIME types!")

if __name__ == "__main__":
    main()
