## 1. Login and get access_token

curl --location 'https://cms.copula.site/auth/login' \
--header 'Content-Type: application/json' \
--data-raw '{
  "email": "<EMAIL>",
  "password": "123456"
}'

sample response:
```
{
    "data": {
        "expires": 900000,
        "refresh_token": "dMGJ2q7estP86aYO0R4N0TboCIQjbrrq5cwbiDvXiuDcwkiT1jMufz8334ARruQC",
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6ImU0M2I3ZmFiLTY2MjItNDdmMi1iOWUyLTgyZjMzYzY1MTRmZSIsInJvbGUiOiJiOWE2ZGZmYS0wNjJiLTRhMjQtYjgzOC04YjkzZDRhOTExNDciLCJhcHBfYWNjZXNzIjp0cnVlLCJhZG1pbl9hY2Nlc3MiOmZhbHNlLCJpYXQiOjE3NTY5OTY4MDMsImV4cCI6MTc1Njk5NzcwMywiaXNzIjoiZGlyZWN0dXMifQ.CGWPcUyTYB9FDtU8Ud5u_nHfvj8WOvwbDZ73z6y_blk"
    }
}
```

## 2. Get User Profile:
from JWT token, we already know the user_id (in this case, e43b7fab-6622-47f2-b9e2-82f33c6514fe), so we can get the user profile via its user_id:

curl --location  'https://cms.copula.site/items/UserProfile?filter[user][_eq]=e43b7fab-6622-47f2-b9e2-82f33c6514fe' \
--header 'accept: application/json, text/plain, */*' \
--header 'Cookie: directus_session_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6ImU0M2I3ZmFiLTY2MjItNDdmMi1iOWUyLTgyZjMzYzY1MTRmZSIsInJvbGUiOiJiOWE2ZGZmYS0wNjJiLTRhMjQtYjgzOC04YjkzZDRhOTExNDciLCJhcHBfYWNjZXNzIjp0cnVlLCJhZG1pbl9hY2Nlc3MiOmZhbHNlLCJpYXQiOjE3NTcwMDYwMzgsImV4cCI6MTc1NzAwNjkzOCwiaXNzIjoiZGlyZWN0dXMifQ.Y1J-WRkfPjBWEq3Q-xivQVeJRiJ0nyRQtdnjVH6qo3Y'


sample response:
```
{
    "data": [
        {
            "id": 2,
            "status": "published",
            "user": "e43b7fab-6622-47f2-b9e2-82f33c6514fe",
            "first_name": null,
            "last_name": null,
            "height": null,
            "marriage_status": null,
            "race": null,
            "age": null,
            "avatar": null,
            "desired_relationship": null,
            "essay": null,
            "smoke": null,
            "pets": null,
            "location": null,
            "income": null,
            "job": null,
            "education": null,
            "drink": null,
            "body_type": null,
            "gender": null,
            "images": []
        }
    ]
}
```

## 3. Update User Profile:
From API #2, we already know the user_profile_id (in this case, 2), so we can update the user profile via its user_profile_id:
Because we are using PATCH, we only need to provide the fields that we want to update.

curl --location --request PATCH 'https://cms.copula.site/items/UserProfile/2' \
--header 'accept: application/json, text/plain, */*' \
--header 'content-type: application/json' \
--header 'Cookie: directus_session_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6ImU0M2I3ZmFiLTY2MjItNDdmMi1iOWUyLTgyZjMzYzY1MTRmZSIsInJvbGUiOiJiOWE2ZGZmYS0wNjJiLTRhMjQtYjgzOC04YjkzZDRhOTExNDciLCJhcHBfYWNjZXNzIjp0cnVlLCJhZG1pbl9hY2Nlc3MiOmZhbHNlLCJpYXQiOjE3NTcwMDYwMzgsImV4cCI6MTc1NzAwNjkzOCwiaXNzIjoiZGlyZWN0dXMifQ.Y1J-WRkfPjBWEq3Q-xivQVeJRiJ0nyRQtdnjVH6qo3Y' \
--data '{"age":34,"first_name":"My first name","race":"asian"}'


sample response:

```
{
    "data": {
        "id": 2,
        "status": "published",
        "user": "e43b7fab-6622-47f2-b9e2-82f33c6514fe",
        "first_name": "My first name",
        "last_name": null,
        "height": null,
        "marriage_status": null,
        "race": "asian",
        "age": 34,
        "avatar": null,
        "desired_relationship": null,
        "essay": null,
        "smoke": null,
        "pets": null,
        "location": null,
        "income": null,
        "job": null,
        "education": null,
        "drink": null,
        "body_type": null,
        "gender": null,
        "images": []
    }
}
```

## 4. Upload Image:

### 4.1 To upload any image, we need to provide image location (in this case, "/Users/<USER>/Downloads/image1.jpg"):

curl --location 'https://cms.copula.site/files' \
--header 'accept: application/json, text/plain, */*' \
--header 'Cookie: directus_session_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6ImU0M2I3ZmFiLTY2MjItNDdmMi1iOWUyLTgyZjMzYzY1MTRmZSIsInJvbGUiOiJiOWE2ZGZmYS0wNjJiLTRhMjQtYjgzOC04YjkzZDRhOTExNDciLCJhcHBfYWNjZXNzIjp0cnVlLCJhZG1pbl9hY2Nlc3MiOmZhbHNlLCJpYXQiOjE3NTY5OTY4MDMsImV4cCI6MTc1Njk5NzcwMywiaXNzIjoiZGlyZWN0dXMifQ.CGWPcUyTYB9FDtU8Ud5u_nHfvj8WOvwbDZ73z6y_blk' \
--form 'file=@"/Users/<USER>/Downloads/image1.jpg"'

sample response:

```
{
    "data": {
        "id": "3a155714-540d-4bc2-a0cb-7f2b04506fb6",
        "storage": "gcs",
        "filename_disk": "3a155714-540d-4bc2-a0cb-7f2b04506fb6.jpg",
        "filename_download": "image1.jpg",
        "title": "Image1",
        "type": "image/jpeg",
        "folder": null,
        "uploaded_by": "e43b7fab-6622-47f2-b9e2-82f33c6514fe",
        "created_on": "2025-09-04T17:38:41.346Z",
        "modified_by": null,
        "modified_on": "2025-09-04T17:38:41.544Z",
        "charset": null,
        "filesize": "91613",
        "width": 1280,
        "height": 800,
        "duration": null,
        "embed": null,
        "description": null,
        "location": null,
        "tags": null,
        "metadata": {},
        "focal_point_x": null,
        "focal_point_y": null,
        "tus_id": null,
        "tus_data": null,
        "uploaded_on": "2025-09-04T17:38:41.541Z"
    }
}
```

### 4.2 To access the uploaded image, we used its id (in this case, 3a155714-540d-4bc2-a0cb-7f2b04506fb6) to get the image:
https://cms.copula.site/assets/3a155714-540d-4bc2-a0cb-7f2b04506fb6 


### 4.3 To resize image, we can use the following API:
https://cms.copula.site/assets/3a155714-540d-4bc2-a0cb-7f2b04506fb6?width=400&height=600


## 5. Upload Avatar:
To upload Avatar, first, we need to upload the image using ##4, and then we can update the user profile with the image id:

curl --location --request PATCH 'https://cms.copula.site/items/UserProfile/2' \
--header 'accept: application/json, text/plain, */*' \
--header 'content-type: application/json' \
--header 'Cookie: directus_session_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjZiZmEwYTA2LTI1MzgtNDhiNi04YzZmLTE2M2MwZDdmNWZiNSIsInJvbGUiOiJiYjVmODQyMy02Y2FlLTRmMWQtOTg2OS01MTY2N2Q5NTFmNGUiLCJhcHBfYWNjZXNzIjp0cnVlLCJhZG1pbl9hY2Nlc3MiOnRydWUsInNlc3Npb24iOiJmOTJkTEZrZGQwS1ZkcWlkZUJqWGxfS245SnFER3MzVHdzQU9HMFc3TXdYOGlLYjZkN1BhWnVwVVBFRkt6SVp6IiwiaWF0IjoxNzU2OTk1Nzk3LCJleHAiOjE3NTcwODIxOTcsImlzcyI6ImRpcmVjdHVzIn0.PdsmHfzDMdbhtEhBRC7Tdl7AunKKWUWoNrOSjP1X1q8' \
--data '{"avatar": "3a155714-540d-4bc2-a0cb-7f2b04506fb6"}'

sample response:
```
{
    "data": {
        "id": 2,
        "status": "published",
        "user": "e43b7fab-6622-47f2-b9e2-82f33c6514fe",
        "first_name": "My first name",
        "last_name": null,
        "height": null,
        "marriage_status": null,
        "race": "asian",
        "age": 34,
        "avatar": "001827ea-dfae-48a6-a352-cbd21b5e2117",
        "desired_relationship": null,
        "essay": null,
        "smoke": null,
        "pets": null,
        "location": null,
        "income": null,
        "job": null,
        "education": null,
        "drink": null,
        "body_type": null,
        "gender": null,
        "images": []
    }
}
```

## 6. Upload multiple images to User Profile:

First, we need to upload the images using ##4, 
Then we can update the user profile with the image ids:

curl --location --request PATCH 'https://cms.copula.site/items/UserProfile/2' \
--header 'accept: application/json, text/plain, */*' \
--header 'content-type: application/json' \
--header 'Cookie: directus_session_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6ImU0M2I3ZmFiLTY2MjItNDdmMi1iOWUyLTgyZjMzYzY1MTRmZSIsInJvbGUiOiJiOWE2ZGZmYS0wNjJiLTRhMjQtYjgzOC04YjkzZDRhOTExNDciLCJhcHBfYWNjZXNzIjp0cnVlLCJhZG1pbl9hY2Nlc3MiOmZhbHNlLCJpYXQiOjE3NTY5OTY4MDMsImV4cCI6MTc1Njk5NzcwMywiaXNzIjoiZGlyZWN0dXMifQ.CGWPcUyTYB9FDtU8Ud5u_nHfvj8WOvwbDZ73z6y_blk' \
--data '{"images":{"create":[{"UserProfile_id":"2","directus_files_id":{"id":"5b0e1124-dccd-4be7-8007-e0e7f3843cbd"}},{"UserProfile_id":"2","directus_files_id":{"id":"95f84de1-e887-41d7-a9e4-c1d23cf759a6"}}]}}'

sample response:
```
{
    "data": {
        "id": 2,
        "status": "published",
        "user": "e43b7fab-6622-47f2-b9e2-82f33c6514fe",
        "first_name": "My first name",
        "last_name": null,
        "height": null,
        "marriage_status": null,
        "race": "asian",
        "age": 34,
        "avatar": "001827ea-dfae-48a6-a352-cbd21b5e2117",
        "desired_relationship": null,
        "essay": null,
        "smoke": null,
        "pets": null,
        "location": null,
        "income": null,
        "job": null,
        "education": null,
        "drink": null,
        "body_type": null,
        "gender": null,
        "images": [
            5,
            6
        ]
    }
}
```

as we see, the user profile now has two images, with ids 5 and 6, but we need to know its uuid to access the images, to do this, we can use the following API:

curl --location --globoff 'https://cms.copula.site/items/UserProfile?fields=images.directus_files_id.id&filter\[user\]\[_eq\]=e43b7fab-6622-47f2-b9e2-82f33c6514fe' \
--header 'accept: application/json, text/plain, */*' \
--header 'Cookie: directus_session_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6ImU0M2I3ZmFiLTY2MjItNDdmMi1iOWUyLTgyZjMzYzY1MTRmZSIsInJvbGUiOiJiOWE2ZGZmYS0wNjJiLTRhMjQtYjgzOC04YjkzZDRhOTExNDciLCJhcHBfYWNjZXNzIjp0cnVlLCJhZG1pbl9hY2Nlc3MiOmZhbHNlLCJpYXQiOjE3NTcwMDYwMzgsImV4cCI6MTc1NzAwNjkzOCwiaXNzIjoiZGlyZWN0dXMifQ.Y1J-WRkfPjBWEq3Q-xivQVeJRiJ0nyRQtdnjVH6qo3Y'

sample response:
```
{
    "data": [
        {
            "images": [
                {
                    "directus_files_id": {
                        "id": "5b0e1124-dccd-4be7-8007-e0e7f3843cbd"
                    }
                },
                {
                    "directus_files_id": {
                        "id": "95f84de1-e887-41d7-a9e4-c1d23cf759a6"
                    }
                }
            ]
        }
    ]
}
```

actually, this API is the ##2 API, but we include a new parameter "images.directus_files_id.id" to get the image uuid.

## 7. Generate image: TBD

## 8. Get Matching Users: TBD