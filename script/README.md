# User Profile Creator Script

This script implements the strategy outlined in `strategy.md` to create users and update their profiles using data from CSV files.

## Overview

The script performs the following operations for each CSV record:

1. **Create User** - Creates a new user via API 1 (`POST /users`)
2. **Get Profile** - Retrieves the user profile via API 2 (`GET /items/UserProfile`)
3. **Update Profile** - Updates the profile with CSV data via API 3 (`PATCH /items/UserProfile/{id}`)

## Files

- `user_profile_creator.py` - Main script for creating and updating profiles
- `verify_profile.py` - Verification script to check created profiles
- `strategy.md` - Original strategy document with API details

## CSV Data Mapping

The script maps CSV fields to API fields according to the strategy:

| CSV Field | API Field | Notes |
|-----------|-----------|-------|
| `id` | `sample_id` | As specified in strategy |
| `essay0` | `essay` | As specified in strategy |
| `height_cm` | `height` | As specified in strategy |
| `first_name` | `first_name` | Direct mapping |
| `last_name` | `last_name` | Direct mapping |
| `age` | `age` | Direct mapping |
| `race` | `race` | Direct mapping |
| `gender` | `gender` | Direct mapping |
| `education` | `education` | Direct mapping |
| `job` | `work` | Direct mapping |
| `location` | `city` | Direct mapping |
| `desired_relationship` | `desired_relationship` | Direct mapping |
| `smokes` | `smoke` | Direct mapping |
| `pets` | `pets` | Direct mapping |
| `income` | `income` | Direct mapping |
| `drinks` | `drink` | Direct mapping |
| `body_type` | `body_type` | Direct mapping |
| `openness` | `openness` | Direct mapping |
| `conscientiousness` | `conscientiousness` | Direct mapping |
| `extraversion` | `extraversion` | Direct mapping |
| `agreeableness` | `agreeableness` | Direct mapping |
| `neuroticism` | `neuroticism` | Direct mapping |
| `interests` | `interests` | Direct mapping |
| `family_plan` | `family_plan` | Direct mapping |

**Ignored fields** (as per strategy): `file_name`, `image_source`

## Usage

### Basic Usage (Test Mode)

Test with the first record only:

```bash
python3 user_profile_creator.py
```

### Process All Records

Process all records in the CSV file:

```bash
python3 user_profile_creator.py --all
```

### Advanced Options

```bash
# Start from a specific record (0-based index)
python3 user_profile_creator.py --all --start 10

# Process only a limited number of records
python3 user_profile_creator.py --all --limit 5

# Start from record 10 and process 5 records
python3 user_profile_creator.py --all --start 10 --limit 5

# Use a specific CSV file
python3 user_profile_creator.py --csv /path/to/your/file.csv

# Get help
python3 user_profile_creator.py --help
```

### Verification

To verify that profiles were created correctly:

```bash
python3 verify_profile.py
```

## CSV File Selection

The script automatically selects the appropriate CSV file:

1. **First choice**: `new_data.csv` (if it has the `essay0` field)
2. **Fallback**: `../files/dating_app_sample_100_essay_tag.csv`

You can override this with the `--csv` parameter.

## Configuration

The script uses these configuration values (defined at the top of the file):

```python
BASE_URL = "https://cms.copula.site"
SESSION_TOKEN = "pcTMayE-BZ7kqVkF9Va6wlhIe5D5KVGO"
ROLE_ID = "b9a6dffa-062b-4a24-b838-8b93d4a91147"
DEFAULT_PASSWORD = "123456"
```

## Error Handling

The script includes comprehensive error handling:

- **Duplicate emails**: Skips records if user email already exists
- **API failures**: Logs errors and continues with next record
- **Missing data**: Handles missing or invalid CSV fields gracefully
- **Rate limiting**: Includes delays between API calls

## Output

The script provides detailed output including:

- Progress indicators for each step
- Success/failure status for each record
- Final statistics (total processed, successful, failed, success rate)
- Detailed error messages for debugging

## Example Output

```
🚀 User Profile Creator Script
Based on strategy.md requirements

📋 Using dating_app_sample_100_essay_tag.csv
🧪 Testing with first record only...
📁 Reading CSV file: /path/to/file.csv

============================================================
Processing record 1: ID 11462 (#1)
============================================================
✓ Created user: <EMAIL> with ID: e8cd3eee-978a-4267-882c-53d88b5045c8
✓ Found user profile ID: 5 for user: e8cd3eee-978a-4267-882c-53d88b5045c8
📝 Profile data to update: {...}
✓ Updated profile ID: 5
✅ Successfully processed record 1

📊 Final Statistics:
  Total processed: 1
  Successful: 1
  Failed: 0
  Success rate: 100.0%

🎉 Script completed!
```

## Testing

The script has been tested with:

- ✅ First record (ID: 11462) - Successfully created and updated
- ✅ Second record (ID: 12327) - Successfully created and updated
- ✅ Duplicate handling - Correctly skips existing users
- ✅ Field mapping - All CSV fields correctly mapped to API fields
- ✅ Error handling - Gracefully handles API errors

## Requirements

- Python 3.6+
- `requests` library
- Valid session token and API access

## Security Notes

- Session token is hardcoded in the script (consider using environment variables for production)
- Default password is used for all created users
- API calls are made over HTTPS

## Next Steps

After testing with the first record, you can:

1. Run with `--all` to process all records
2. Use `--start` and `--limit` for batch processing
3. Monitor the output for any failures
4. Use the verification script to confirm data integrity
