# Execute Workflow API - Automatic Image Upload Enhancement

## 🎯 Overview

The `execute-workflow` API has been enhanced to automatically upload images to the correct Google Cloud Storage input folder if they're not already in the expected location. This ensures that all workflow executions use images from the standardized input folder path.

## 🔧 Enhancement Details

### **Problem Solved**
Previously, if an image URL pointed to a location outside the expected input folder (`comfyui-data-analytic-project-424703/comfyui/input/`), the workflow execution might fail or behave unexpectedly.

### **Solution Implemented**
The API now automatically:
1. **Detects** if the provided image URL is not in the input folder
2. **Downloads** the image from the provided URL
3. **Uploads** it to the correct input folder in GCS
4. **Uses** the new input folder URL for workflow execution
5. **Reports** the image processing details in the response

## 📊 Expected Input Folder

**Target Location:** `https://storage.googleapis.com/comfyui-data-analytic-project-424703/comfyui/input/`

### **Examples of URLs that trigger upload:**
- ✅ `https://storage.googleapis.com/comfyui-data-analytic-project-424703/comfyui/common/F_28_image_104.png`
- ✅ `https://storage.googleapis.com/comfyui-data-analytic-project-424703/comfyui/output/some_image.png`
- ✅ `https://external-site.com/image.jpg`
- ✅ `https://picsum.photos/512/512`

### **Examples of URLs that DON'T trigger upload:**
- ❌ `https://storage.googleapis.com/comfyui-data-analytic-project-424703/comfyui/input/F_21_image_85.png`

## 🔄 Process Flow

```mermaid
graph TD
    A[API Request with image_url] --> B{Is image in input folder?}
    B -->|Yes| C[Use image URL directly]
    B -->|No| D[Download image from URL]
    D --> E[Upload to input folder]
    E --> F[Generate new input folder URL]
    F --> G[Execute workflow with new URL]
    C --> G
    G --> H[Return response with image processing info]
```

## 📋 API Request/Response

### **Request Format (Unchanged)**
```json
{
    "prompt": "A beautiful landscape with mountains",
    "image_url": "https://storage.googleapis.com/comfyui-data-analytic-project-424703/comfyui/common/F_28_image_104.png",
    "weight": 0.7,
    "cloudrun_url": "https://comfyui-api-424703-comfyui-api.a.run.app",
    "output_node": "save_image_websocket_node"
}
```

### **Enhanced Response Format**
```json
{
    "success": true,
    "message": "Workflow executed successfully",
    "data": {
        "output_url": "https://storage.googleapis.com/...",
        "execution_time_seconds": 15.23,
        "total_api_time_seconds": 16.45,
        "filename": "output_image.png",
        "input": {
            "prompt": "A beautiful landscape with mountains",
            "image_url": "https://storage.googleapis.com/comfyui-data-analytic-project-424703/comfyui/common/F_28_image_104.png",
            "weight": 0.7,
            "cloudrun_url": "https://comfyui-api-424703-comfyui-api.a.run.app",
            "output_node": "save_image_websocket_node"
        },
        "workflow_details": {
            "enhanced_prompt": "A beautiful landscape with mountains blur street background...",
            "input_image_url": "https://storage.googleapis.com/comfyui-data-analytic-project-424703/comfyui/input/20250710_183051_d52d7c55.png",
            "weight_used": 0.7,
            "output_node_used": "save_image_websocket_node"
        },
        "image_processing": {
            "original_image_url": "https://storage.googleapis.com/comfyui-data-analytic-project-424703/comfyui/common/F_28_image_104.png",
            "processed_image_url": "https://storage.googleapis.com/comfyui-data-analytic-project-424703/comfyui/input/20250710_183051_d52d7c55.png",
            "was_uploaded_to_input_folder": true,
            "input_folder_path": "comfyui-data-analytic-project-424703/comfyui/input/"
        }
    }
}
```

### **Error Response with Image Processing Info**
If workflow execution fails after successful image upload:
```json
{
    "detail": {
        "error": "Error communicating with ComfyUI service: ...",
        "image_processing": {
            "original_image_url": "https://storage.googleapis.com/comfyui-data-analytic-project-424703/comfyui/common/F_28_image_104.png",
            "processed_image_url": "https://storage.googleapis.com/comfyui-data-analytic-project-424703/comfyui/input/20250710_183051_d52d7c55.png",
            "was_uploaded_to_input_folder": true,
            "input_folder_path": "comfyui-data-analytic-project-424703/comfyui/input/",
            "note": "Image was successfully uploaded to input folder before workflow execution failed"
        }
    }
}
```

## 🔍 Image Processing Details

### **New Response Fields**
- `image_processing.original_image_url`: The URL provided in the request
- `image_processing.processed_image_url`: The final URL used for workflow execution
- `image_processing.was_uploaded_to_input_folder`: Boolean indicating if upload occurred
- `image_processing.input_folder_path`: The target input folder path
- `image_processing.note`: Additional information (in error responses)

### **Upload Behavior**
1. **Filename Generation**: Uses timestamp + random suffix for uniqueness
2. **Content Type Validation**: Ensures the URL points to an image
3. **Error Handling**: Provides clear error messages for invalid URLs
4. **Public Access**: Uploaded images are made publicly accessible

## 🧪 Testing Examples

### **Test Case 1: Image Upload Required**
```bash
curl -X POST "http://localhost:8200/execute-workflow" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "A beautiful landscape",
    "image_url": "https://storage.googleapis.com/comfyui-data-analytic-project-424703/comfyui/common/F_28_image_104.png",
    "weight": 0.7,
    "cloudrun_url": "https://comfyui-api-424703-comfyui-api.a.run.app",
    "output_node": "save_image_websocket_node"
  }'
```

**Expected Behavior:**
- ✅ Image downloaded from common folder
- ✅ Image uploaded to input folder with new filename
- ✅ Workflow executed with input folder URL
- ✅ Response includes `was_uploaded_to_input_folder: true`

### **Test Case 2: No Upload Required**
```bash
curl -X POST "http://localhost:8200/execute-workflow" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "A beautiful landscape",
    "image_url": "https://storage.googleapis.com/comfyui-data-analytic-project-424703/comfyui/input/F_21_image_85.png",
    "weight": 0.5,
    "cloudrun_url": "https://comfyui-api-424703-comfyui-api.a.run.app",
    "output_node": "save_image_websocket_node"
  }'
```

**Expected Behavior:**
- ✅ Image URL used directly (no upload)
- ✅ Workflow executed with original URL
- ✅ Response includes `was_uploaded_to_input_folder: false`

### **Test Case 3: External Image URL**
```bash
curl -X POST "http://localhost:8200/execute-workflow" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "A test image",
    "image_url": "https://picsum.photos/512/512",
    "weight": 0.5,
    "cloudrun_url": "https://comfyui-api-424703-comfyui-api.a.run.app",
    "output_node": "save_image_websocket_node"
  }'
```

**Expected Behavior:**
- ✅ External image downloaded
- ✅ Image uploaded to input folder
- ✅ Workflow executed with input folder URL
- ✅ Response includes upload details

## ⚠️ Error Handling

### **Invalid Image URLs**
- **Non-existent URLs**: Returns 400 with "Failed to download image"
- **Non-image content**: Returns 400 with "does not point to an image"
- **Invalid URL format**: Returns 400 with validation error

### **GCS Upload Failures**
- **Storage unavailable**: Returns 503 with "GCP Storage service is not available"
- **Upload failure**: Returns 500 with "Failed to upload downloaded image"

### **Workflow Execution Failures**
- **ComfyUI service down**: Returns error with image processing info (if upload occurred)
- **Invalid parameters**: Returns 400 with parameter validation errors

## 🎯 Benefits

### **1. Consistency**
- All workflow executions use images from the standardized input folder
- Predictable image URL patterns for downstream processing

### **2. Flexibility**
- Supports images from any accessible URL
- Handles both internal GCS URLs and external image sources

### **3. Transparency**
- Clear reporting of image processing actions
- Detailed information about original vs processed URLs

### **4. Error Recovery**
- Even if workflow fails, uploaded images remain available
- Clear indication of successful upload vs workflow failure

### **5. Backward Compatibility**
- Existing API calls continue to work unchanged
- No breaking changes to request format

## 📈 Performance Impact

### **Upload Time**
- **Small images (< 1MB)**: ~1-3 seconds additional processing time
- **Large images (> 5MB)**: ~5-10 seconds additional processing time
- **Network dependent**: External URLs may take longer

### **Storage Usage**
- Images are duplicated in input folder (original remains in source location)
- Unique filenames prevent conflicts
- Consider cleanup policies for old uploaded images

## 🔧 Technical Implementation

### **Key Functions**
- `upload_image_from_url_to_input_folder()`: Main upload logic
- Enhanced error handling in `execute-workflow` endpoint
- Image URL validation and content type checking

### **Dependencies**
- Google Cloud Storage client
- Requests library for image downloading
- PIL/Pillow for image validation (existing)

### **Configuration**
- `BUCKET_NAME`: Target GCS bucket
- `FOLDER_PATH`: Input folder path within bucket
- `SUPPORTED_IMAGE_TYPES`: Allowed image content types

## 📋 Summary

The enhanced `execute-workflow` API provides:

- ✅ **Automatic image upload** to correct input folder
- ✅ **Transparent processing** with detailed reporting
- ✅ **Robust error handling** with clear messages
- ✅ **Backward compatibility** with existing code
- ✅ **Support for external images** from any accessible URL
- ✅ **Consistent workflow execution** using standardized paths

This enhancement ensures reliable workflow execution regardless of the source location of input images! 🚀
