#!/bin/sh

# PostgreSQL Backup Script for Directus CMS
# Runs daily at 2 AM via cron

# Configuration
BAC<PERSON>UP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="directus_backup_${DATE}.sql"
BACKUP_PATH="${BACKUP_DIR}/${BACKUP_FILE}"

# Create backup directory if it doesn't exist
mkdir -p ${BACKUP_DIR}

# Create database backup
echo "Starting backup at $(date)"
pg_dump -h ${PGHOST} -p ${PGPORT} -U ${PGUSER} -d ${PGDATABASE} > ${BACKUP_PATH}

# Check if backup was successful
if [ $? -eq 0 ]; then
    echo "Backup completed successfully: ${BACKUP_FILE}"
    
    # Compress the backup
    gzip ${BACKUP_PATH}
    echo "Backup compressed: ${BACKUP_FILE}.gz"
    
    # Keep only last 30 days of backups
    find ${BACKUP_DIR} -name "directus_backup_*.sql.gz" -mtime +30 -delete
    echo "Old backups cleaned up (kept last 30 days)"
    
    echo "Backup process completed at $(date)"
else
    echo "Backup failed at $(date)"
    exit 1
fi
