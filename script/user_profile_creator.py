#!/usr/bin/env python3
"""
Script to create users and update their profiles based on CSV data.
Implements the strategy from strategy.md:
1. Create new user via API
2. Read user profile via API
3. Update user profile with CSV data
"""

import csv
import requests
import json
import time
import sys
from pathlib import Path

# Configuration
BASE_URL = "https://cms.copula.site"
SESSION_TOKEN = "pcTMayE-BZ7kqVkF9Va6wlhIe5D5KVGO"
ROLE_ID = "b9a6dffa-062b-4a24-b838-8b93d4a91147"
DEFAULT_PASSWORD = "123456"

# Headers for API requests
HEADERS = {
    'accept': 'application/json, text/plain, */*',
    'content-type': 'application/json',
    'Cookie': f'directus_session_token={SESSION_TOKEN}'
}

def create_user(email):
    """
    Create a new user via API 1
    
    Args:
        email (str): Email for the new user
        
    Returns:
        str: User ID if successful, None if failed
    """
    url = f"{BASE_URL}/users"
    data = {
        "role": ROLE_ID,
        "email": email,
        "password": DEFAULT_PASSWORD
    }
    
    try:
        response = requests.post(url, headers=HEADERS, json=data)
        response.raise_for_status()
        
        result = response.json()
        user_id = result.get('data', {}).get('id')
        print(f"✓ Created user: {email} with ID: {user_id}")
        return user_id
        
    except requests.exceptions.RequestException as e:
        print(f"✗ Failed to create user {email}: {e}")
        if hasattr(e, 'response') and e.response is not None:
            print(f"Response: {e.response.text}")
        return None

def get_user_profile(user_id):
    """
    Get user profile via API 2
    
    Args:
        user_id (str): User ID
        
    Returns:
        int: User profile ID if successful, None if failed
    """
    url = f"{BASE_URL}/items/UserProfile"
    params = {
        'filter[user][_eq]': user_id
    }
    
    try:
        response = requests.get(url, headers=HEADERS, params=params)
        response.raise_for_status()
        
        result = response.json()
        data = result.get('data', [])
        
        if data:
            profile_id = data[0].get('id')
            print(f"✓ Found user profile ID: {profile_id} for user: {user_id}")
            return profile_id
        else:
            print(f"✗ No profile found for user: {user_id}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"✗ Failed to get user profile for {user_id}: {e}")
        if hasattr(e, 'response') and e.response is not None:
            print(f"Response: {e.response.text}")
        return None

def update_user_profile(profile_id, profile_data):
    """
    Update user profile via API 3
    
    Args:
        profile_id (int): User profile ID
        profile_data (dict): Profile data to update
        
    Returns:
        bool: True if successful, False if failed
    """
    url = f"{BASE_URL}/items/UserProfile/{profile_id}"
    
    try:
        response = requests.patch(url, headers=HEADERS, json=profile_data)
        response.raise_for_status()
        
        print(f"✓ Updated profile ID: {profile_id}")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"✗ Failed to update profile {profile_id}: {e}")
        if hasattr(e, 'response') and e.response is not None:
            print(f"Response: {e.response.text}")
        return False

def map_csv_to_profile_data(row):
    """
    Map CSV row data to profile update data
    
    Args:
        row (dict): CSV row data
        
    Returns:
        dict: Mapped profile data
    """
    # Field mappings based on strategy.md
    profile_data = {}
    
    # Direct mappings
    if 'id' in row and row['id']:
        profile_data['sample_id'] = int(row['id'])
    
    if 'essay0' in row and row['essay0']:
        profile_data['essay'] = row['essay0']
    
    if 'height_cm' in row and row['height_cm']:
        profile_data['height'] = float(row['height_cm'])
    
    # Other field mappings
    field_mappings = {
        'first_name': 'first_name',
        'last_name': 'last_name', 
        'age': 'age',
        'race': 'race',
        'gender': 'gender',
        'education': 'education',
        'job': 'work',
        'location': 'city',
        'desired_relationship': 'desired_relationship',
        'smokes': 'smoke',
        'pets': 'pets',
        'income': 'income',
        'drinks': 'drink',
        'body_type': 'body_type',
        'openness': 'openness',
        'conscientiousness': 'conscientiousness',
        'extraversion': 'extraversion',
        'agreeableness': 'agreeableness',
        'neuroticism': 'neuroticism',
        'interests': 'interests',
        'family_plan': 'family_plan'
    }
    
    for csv_field, api_field in field_mappings.items():
        if csv_field in row and row[csv_field] and row[csv_field] != '':
            value = row[csv_field]
            
            # Convert numeric fields
            if api_field in ['age', 'income']:
                try:
                    value = int(float(value))
                except (ValueError, TypeError):
                    continue
            elif api_field in ['openness', 'conscientiousness', 'extraversion', 'agreeableness', 'neuroticism']:
                try:
                    value = float(value)
                except (ValueError, TypeError):
                    continue
                    
            profile_data[api_field] = value
    
    return profile_data

def process_csv_file(csv_file_path, test_first_only=True, start_index=0, limit=None):
    """
    Process CSV file and create/update user profiles

    Args:
        csv_file_path (str): Path to CSV file
        test_first_only (bool): If True, only process first record for testing
        start_index (int): Start from this record index (0-based)
        limit (int): Maximum number of records to process
    """
    if not Path(csv_file_path).exists():
        print(f"✗ CSV file not found: {csv_file_path}")
        return

    print(f"📁 Reading CSV file: {csv_file_path}")

    # Statistics tracking
    stats = {
        'total_processed': 0,
        'successful': 0,
        'failed': 0,
        'skipped': 0
    }

    with open(csv_file_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)

        for i, row in enumerate(reader):
            # Skip records before start_index
            if i < start_index:
                continue

            # Check if we've reached the limit
            if limit and stats['total_processed'] >= limit:
                print(f"🛑 Reached limit of {limit} records")
                break

            if test_first_only and stats['total_processed'] > 0:
                print(f"🛑 Test mode: stopping after first record")
                break

            stats['total_processed'] += 1

            print(f"\n{'='*60}")
            print(f"Processing record {i+1}: ID {row.get('id', 'N/A')} (#{stats['total_processed']})")
            print(f"{'='*60}")

            # Generate unique email
            record_id = row.get('id', f'record_{i+1}')
            email = f"sample_{record_id}@copula.site"

            # Step 1: Create user
            user_id = create_user(email)
            if not user_id:
                print(f"⚠️  Skipping record {i+1} due to user creation failure")
                stats['failed'] += 1
                continue

            # Wait a bit for user creation to complete
            time.sleep(0.5)

            # Step 2: Get user profile
            profile_id = get_user_profile(user_id)
            if not profile_id:
                print(f"⚠️  Skipping record {i+1} due to profile retrieval failure")
                stats['failed'] += 1
                continue

            # Step 3: Map CSV data to profile data
            profile_data = map_csv_to_profile_data(row)
            print(f"📝 Profile data to update: {json.dumps(profile_data, indent=2)}")

            # Step 4: Update user profile
            success = update_user_profile(profile_id, profile_data)
            if success:
                print(f"✅ Successfully processed record {i+1}")
                stats['successful'] += 1
            else:
                print(f"❌ Failed to process record {i+1}")
                stats['failed'] += 1

            # Add delay between requests to be respectful
            time.sleep(0.5)

    # Print final statistics
    print(f"\n📊 Final Statistics:")
    print(f"  Total processed: {stats['total_processed']}")
    print(f"  Successful: {stats['successful']}")
    print(f"  Failed: {stats['failed']}")
    print(f"  Success rate: {(stats['successful']/stats['total_processed']*100):.1f}%" if stats['total_processed'] > 0 else "N/A")

def main():
    """Main function"""
    print("🚀 User Profile Creator Script")
    print("Based on strategy.md requirements")
    print()

    # Check command line arguments
    import argparse
    parser = argparse.ArgumentParser(description='Create users and update profiles from CSV data')
    parser.add_argument('--all', action='store_true', help='Process all records (default: test with first record only)')
    parser.add_argument('--csv', type=str, help='Specify CSV file path (optional)')
    parser.add_argument('--start', type=int, default=0, help='Start from record number (0-based index)')
    parser.add_argument('--limit', type=int, help='Limit number of records to process')
    args = parser.parse_args()

    # Determine which CSV file to use
    if args.csv:
        csv_file_path = Path(args.csv)
        if not csv_file_path.exists():
            print(f"❌ Specified CSV file not found: {csv_file_path}")
            sys.exit(1)
        print(f"📋 Using specified CSV file: {csv_file_path}")
    else:
        # First check if new_data.csv has essay0 field, otherwise use the other file
        script_dir = Path(__file__).parent
        new_data_csv = script_dir / "new_data.csv"
        essay_csv = script_dir.parent / "files" / "dating_app_sample_100_essay_tag.csv"

        csv_file_path = None

        if new_data_csv.exists():
            # Check if new_data.csv has essay0 field
            with open(new_data_csv, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                headers = reader.fieldnames
                if 'essay0' in headers:
                    csv_file_path = new_data_csv
                    print(f"📋 Using new_data.csv (has essay0 field)")
                else:
                    print(f"⚠️  new_data.csv doesn't have essay0 field")

        if not csv_file_path and essay_csv.exists():
            csv_file_path = essay_csv
            print(f"📋 Using dating_app_sample_100_essay_tag.csv")

        if not csv_file_path:
            print("❌ No suitable CSV file found!")
            sys.exit(1)

    # Process CSV file
    if args.all:
        print(f"🚀 Processing ALL records...")
        process_csv_file(csv_file_path, test_first_only=False, start_index=args.start, limit=args.limit)
    else:
        print(f"🧪 Testing with first record only...")
        process_csv_file(csv_file_path, test_first_only=True)

    print(f"\n🎉 Script completed!")

if __name__ == "__main__":
    main()
